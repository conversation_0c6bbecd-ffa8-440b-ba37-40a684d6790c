import type { InputHTMLAttributes } from 'react';
import type { Control, RegisterOptions } from 'react-hook-form';

export type InputPhoneVariant = 'default' | 'with-country-dropdown' | 'form';

export type PhoneFormat = 'international' | 'national' | 'e164';

export type CountryData = {
  alpha2: string;
  alpha3: string;
  countryCallingCodes: string[];
  currencies: string[];
  emoji?: string;
  ioc: string;
  languages: string[];
  name: string;
  status: string;
};

export interface BaseInputPhoneProps extends Omit<InputHTMLAttributes<HTMLInputElement>, 'onChange' | 'value'> {
  variant?: InputPhoneVariant;
  value?: string;
  onChange?: (value: string) => void;
  onCountryChange?: (country: CountryData | undefined) => void;
  defaultCountry?: string;
  showFlag?: boolean;
  format?: PhoneFormat;
  className?: string;
  placeholder?: string;
}

export interface DefaultPhoneProps extends BaseInputPhoneProps {
  variant?: 'default';
}

export interface WithCountryDropdownProps extends BaseInputPhoneProps {
  variant: 'with-country-dropdown';
  onCountrySelect?: (countryCode: string) => void;
  dropdownClassName?: string;
  searchable?: boolean;
}

export interface FormPhoneProps extends BaseInputPhoneProps {
  variant: 'form';
  name: string;
  control: Control<any>;
  label?: string;
  description?: string;
  rules?: RegisterOptions;
}

export type InputPhoneProps = 
  | DefaultPhoneProps
  | WithCountryDropdownProps  
  | FormPhoneProps;