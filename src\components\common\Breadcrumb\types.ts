import type { ReactNode } from 'react';

export interface BreadcrumbItemData {
  label: string;
  href?: string;
  icon?: ReactNode;
  isCurrentPage?: boolean;
}

export interface BreadcrumbProps {
  items: BreadcrumbItemData[];
  variant?: 'default' | 'compact' | 'with-icons' | 'separated';
  size?: 'sm' | 'md' | 'lg';
  separator?: ReactNode;
  maxItems?: number;
  showHome?: boolean;
  className?: string;
  onItemClick?: (item: BreadcrumbItemData, index: number) => void;
}