import { create } from "zustand";
import { devtools } from "zustand/middleware";

interface Toast {
	id: string;
	type: "success" | "error" | "warning" | "info";
	title: string;
	message?: string;
	duration?: number;
}

interface UIState {
	// Navigation
	sidebarCollapsed: boolean;
	mobileMenuOpen: boolean;
	expandedNavItem: string | null;
	activeTab: string;
	breadcrumbs: Array<{ label: string; href?: string }>;

	// Modals & Overlays
	activeModal: string | null;
	modalData: Record<string, any>;
	loadingStates: Record<string, boolean>;

	// Notifications
	toasts: Toast[];

	// Theme & Preferences
	theme: "light" | "dark" | "system";
	density: "comfortable" | "compact";

	// Actions
	toggleSidebar: () => void;
	setSidebarCollapsed: (collapsed: boolean) => void;
	setMobileMenuOpen: (open: boolean) => void;
	setExpandedNavItem: (itemId: string | null) => void;
	toggleNavItem: (itemId: string) => void;
	setActiveTab: (tab: string) => void;
	setBreadcrumbs: (breadcrumbs: UIState["breadcrumbs"]) => void;

	openModal: (modalId: string, data?: Record<string, any>) => void;
	closeModal: () => void;

	setLoading: (key: string, loading: boolean) => void;

	addToast: (toast: Omit<Toast, "id">) => void;
	removeToast: (id: string) => void;
	clearToasts: () => void;

	setTheme: (theme: UIState["theme"]) => void;
	setDensity: (density: UIState["density"]) => void;
}

export const useUIStore = create<UIState>()(
	devtools(
		(set, get) => ({
			// Initial state
			sidebarCollapsed: false,
			mobileMenuOpen: false,
			expandedNavItem: null,
			activeTab: "overview",
			breadcrumbs: [],
			activeModal: null,
			modalData: {},
			loadingStates: {},
			toasts: [],
			theme: "system",
			density: "comfortable",

			// Navigation actions
			toggleSidebar: () =>
				set((state) => {
					// On mobile, toggle the mobile menu
					// On desktop, toggle collapsed state
					const isMobile = window.innerWidth < 768;
					if (isMobile) {
						return { mobileMenuOpen: !state.mobileMenuOpen };
					} else {
						return { sidebarCollapsed: !state.sidebarCollapsed };
					}
				}),

			setSidebarCollapsed: (collapsed) =>
				set({ sidebarCollapsed: collapsed }),

			setMobileMenuOpen: (open) => set({ mobileMenuOpen: open }),

			setExpandedNavItem: (itemId) => set({ expandedNavItem: itemId }),

			toggleNavItem: (itemId) =>
				set((state) => ({
					expandedNavItem:
						state.expandedNavItem === itemId ? null : itemId,
				})),

			setActiveTab: (tab) => set({ activeTab: tab }),

			setBreadcrumbs: (breadcrumbs) => set({ breadcrumbs }),

			// Modal actions
			openModal: (modalId, data = {}) =>
				set({
					activeModal: modalId,
					modalData: data,
				}),

			closeModal: () =>
				set({
					activeModal: null,
					modalData: {},
				}),

			// Loading states
			setLoading: (key, loading) =>
				set((state) => ({
					loadingStates: {
						...state.loadingStates,
						[key]: loading,
					},
				})),

			// Toast notifications
			addToast: (toast) => {
				const id = crypto.randomUUID();
				const newToast = { ...toast, id };

				set((state) => ({
					toasts: [...state.toasts, newToast],
				}));

				// Auto-remove toast after duration
				const duration = toast.duration ?? 5000;
				if (duration > 0) {
					setTimeout(() => {
						get().removeToast(id);
					}, duration);
				}
			},

			removeToast: (id) =>
				set((state) => ({
					toasts: state.toasts.filter((t) => t.id !== id),
				})),

			clearToasts: () => set({ toasts: [] }),

			// Theme actions
			setTheme: (theme) => set({ theme }),
			setDensity: (density) => set({ density }),
		}),
		{ name: "UIStore" }
	)
);
