import * as React from "react"
import { Check, ChevronsUpDown, Search } from "lucide-react"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Command as CommandPrimitive,
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
  CommandShortcut,
} from "@/components/ui/command"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import type { CommandProps, CommandItem as CommandItemType, CommandGroup as CommandGroupType } from "./types"

// Size variants
const sizeVariants = {
  sm: {
    container: "min-w-[200px]",
    input: "h-8 text-sm",
    item: "px-2 py-1 text-sm",
    trigger: "h-8 text-sm px-2"
  },
  md: {
    container: "min-w-[300px]",
    input: "h-9 text-sm",
    item: "px-2 py-1.5 text-sm",
    trigger: "h-9 text-sm px-3"
  },
  lg: {
    container: "min-w-[450px]",
    input: "h-10 text-base",
    item: "px-3 py-2 text-base",
    trigger: "h-10 text-base px-4"
  }
}

// Render command content (groups and items)
const renderCommandContent = (
  groups: CommandGroupType[],
  onItemSelect?: (item: CommandItemType) => void,
  size: 'sm' | 'md' | 'lg' = 'md'
) => {
  return groups.map((group, groupIndex) => (
    <React.Fragment key={groupIndex}>
      {groupIndex > 0 && <CommandSeparator />}
      <CommandGroup heading={group.heading}>
        {group.items.map((item) => (
          <CommandItem
            key={item.id}
            value={item.value || item.label}
            disabled={item.disabled}
            onSelect={() => {
              if (item.onSelect) item.onSelect()
              if (onItemSelect) onItemSelect(item)
            }}
            className={cn(
              "gap-2 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
              sizeVariants[size].item
            )}
          >
            {item.icon}
            <span>{item.label}</span>
            {item.shortcut && <CommandShortcut>{item.shortcut}</CommandShortcut>}
          </CommandItem>
        ))}
      </CommandGroup>
    </React.Fragment>
  ))
}

// Default Command Implementation
const DefaultCommand: React.FC<Extract<CommandProps, { variant?: 'default' }>> = ({
  groups,
  placeholder = "Type a command or search...",
  emptyMessage = "No results found.",
  className,
  size = 'md',
  showSearch = true,
  onItemSelect,
  disabled
}) => {
  return (
    <div
      className={cn(
        "rounded-lg border shadow-md",
        sizeVariants[size].container,
        disabled && "opacity-50 pointer-events-none",
        className
      )}
    >
      <CommandPrimitive>
        {showSearch && (
          <CommandInput 
            placeholder={placeholder}
            className={sizeVariants[size].input}
            disabled={disabled}
          />
        )}
        <CommandList>
          <CommandEmpty>{emptyMessage}</CommandEmpty>
          {renderCommandContent(groups, onItemSelect, size)}
        </CommandList>
      </CommandPrimitive>
    </div>
  )
}

// Dialog Command Implementation
const DialogCommand: React.FC<Extract<CommandProps, { variant: 'dialog' }>> = ({
  groups,
  placeholder = "Type a command or search...",
  emptyMessage = "No results found.",
  open,
  onOpenChange,
  trigger,
  shortcut = "⌘K",
  onItemSelect,
  disabled
}) => {
  const [isOpen, setIsOpen] = React.useState(false)
  const finalOpen = open !== undefined ? open : isOpen
  const finalOnOpenChange = onOpenChange || setIsOpen

  // Keyboard shortcut handler
  React.useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if (e.key === "k" && (e.metaKey || e.ctrlKey)) {
        e.preventDefault()
        finalOnOpenChange(!finalOpen)
      }
    }
    document.addEventListener("keydown", down)
    return () => document.removeEventListener("keydown", down)
  }, [finalOpen, finalOnOpenChange])

  const handleItemSelect = (item: CommandItemType) => {
    if (onItemSelect) onItemSelect(item)
    finalOnOpenChange(false)
  }

  return (
    <>
      {trigger || (
        <Button
          variant="outline"
          className={cn(
            "relative h-9 w-full justify-start text-sm text-muted-foreground sm:pr-12 md:w-40 lg:w-64",
            disabled && "opacity-50 cursor-not-allowed"
          )}
          onClick={() => finalOnOpenChange(true)}
          disabled={disabled}
        >
          <Search className="mr-2 h-4 w-4" />
          <span className="hidden lg:inline-flex">Search...</span>
          <span className="inline-flex lg:hidden">Search</span>
          <kbd className="pointer-events-none absolute right-1.5 top-2 hidden h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium opacity-100 sm:flex">
            <span className="text-xs">{shortcut}</span>
          </kbd>
        </Button>
      )}
      <CommandDialog open={finalOpen} onOpenChange={finalOnOpenChange}>
        <CommandInput placeholder={placeholder} />
        <CommandList>
          <CommandEmpty>{emptyMessage}</CommandEmpty>
          {renderCommandContent(groups, handleItemSelect)}
        </CommandList>
      </CommandDialog>
    </>
  )
}

// Combobox Command Implementation
const ComboboxCommand: React.FC<Extract<CommandProps, { variant: 'combobox' }>> = ({
  options,
  value,
  onChange,
  placeholder = "Select option...",
  emptyMessage = "No option found.",
  trigger,
  searchable = true,
  allowClear = false,
  multiple = false,
  maxSelectedItems,
  size = 'md',
  className,
  disabled
}) => {
  const [open, setOpen] = React.useState(false)
  const [selectedValues, setSelectedValues] = React.useState<string[]>(() => {
    if (multiple) {
      return Array.isArray(value) ? value : value ? [value] : []
    }
    return []
  })
  
  const singleValue = multiple ? undefined : (Array.isArray(value) ? value[0] : value)

  const handleSelect = (selectedValue: string) => {
    if (multiple) {
      let newValues: string[]
      if (selectedValues.includes(selectedValue)) {
        newValues = selectedValues.filter(v => v !== selectedValue)
      } else {
        newValues = [...selectedValues, selectedValue]
        if (maxSelectedItems && newValues.length > maxSelectedItems) {
          newValues = newValues.slice(-maxSelectedItems)
        }
      }
      setSelectedValues(newValues)
      if (onChange) onChange(newValues)
    } else {
      if (onChange) onChange(selectedValue)
      setOpen(false)
    }
  }

  const handleClear = () => {
    if (multiple) {
      setSelectedValues([])
      if (onChange) onChange([])
    } else {
      if (onChange) onChange("")
    }
  }

  const getDisplayValue = () => {
    if (multiple) {
      if (selectedValues.length === 0) return placeholder
      if (selectedValues.length === 1) {
        const option = options.find(opt => opt.value === selectedValues[0] || opt.id === selectedValues[0])
        return option?.label || selectedValues[0]
      }
      return `${selectedValues.length} selected`
    } else {
      if (!singleValue) return placeholder
      const option = options.find(opt => opt.value === singleValue || opt.id === singleValue)
      return option?.label || singleValue
    }
  }

  // Update selectedValues when value prop changes
  React.useEffect(() => {
    if (multiple) {
      const newValues = Array.isArray(value) ? value : value ? [value] : []
      setSelectedValues(newValues)
    }
  }, [value, multiple])

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        {trigger || (
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn(
              "justify-between",
              sizeVariants[size].trigger,
              !multiple && !singleValue && "text-muted-foreground",
              disabled && "opacity-50 cursor-not-allowed",
              className
            )}
            disabled={disabled}
          >
            <span className="truncate">{getDisplayValue()}</span>
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        )}
      </PopoverTrigger>
      <PopoverContent className={cn("p-0", sizeVariants[size].container)}>
        <CommandPrimitive>
          {searchable && (
            <CommandInput placeholder={`Search...`} className={sizeVariants[size].input} />
          )}
          <CommandList>
            <CommandEmpty>{emptyMessage}</CommandEmpty>
            <CommandGroup>
              {allowClear && (multiple ? selectedValues.length > 0 : singleValue) && (
                <CommandItem onSelect={handleClear} className={sizeVariants[size].item}>
                  <span className="text-muted-foreground">Clear selection</span>
                </CommandItem>
              )}
              {options.map((option) => {
                const isSelected = multiple 
                  ? selectedValues.includes(option.value || option.id)
                  : singleValue === (option.value || option.id)
                
                return (
                  <CommandItem
                    key={option.id}
                    value={option.value || option.label}
                    onSelect={() => handleSelect(option.value || option.id)}
                    disabled={option.disabled}
                    className={cn(
                      "gap-2 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
                      sizeVariants[size].item
                    )}
                  >
                    <Check
                      className={cn(
                        "ml-auto h-4 w-4",
                        isSelected ? "opacity-100" : "opacity-0"
                      )}
                    />
                    {option.icon}
                    <span>{option.label}</span>
                  </CommandItem>
                )
              })}
            </CommandGroup>
          </CommandList>
        </CommandPrimitive>
      </PopoverContent>
    </Popover>
  )
}

// Dropdown Command Implementation
const DropdownCommand: React.FC<Extract<CommandProps, { variant: 'dropdown' }>> = ({
  groups,
  trigger,
  placeholder = "Search...",
  emptyMessage = "No results found.",
  onItemSelect,
  align = "start",
  side = "bottom",
  size = 'md',
  className,
  disabled
}) => {
  const [open, setOpen] = React.useState(false)

  const handleItemSelect = (item: CommandItemType) => {
    if (onItemSelect) onItemSelect(item)
    setOpen(false)
  }

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger asChild disabled={disabled}>
        {trigger}
      </DropdownMenuTrigger>
      <DropdownMenuContent align={align} side={side} className={cn("p-0", className)}>
        <CommandPrimitive>
          <CommandInput placeholder={placeholder} className={sizeVariants[size].input} />
          <CommandList>
            <CommandEmpty>{emptyMessage}</CommandEmpty>
            {renderCommandContent(groups, handleItemSelect, size)}
          </CommandList>
        </CommandPrimitive>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

// Inline Command Implementation
const InlineCommand: React.FC<Extract<CommandProps, { variant: 'inline' }>> = ({
  groups,
  placeholder = "Search...",
  emptyMessage = "No results found.",
  showSearch = true,
  onItemSelect,
  size = 'md',
  className,
  disabled
}) => {
  return (
    <div
      className={cn(
        "rounded-lg border",
        sizeVariants[size].container,
        disabled && "opacity-50 pointer-events-none",
        className
      )}
    >
      <CommandPrimitive>
        {showSearch && (
          <CommandInput 
            placeholder={placeholder}
            className={sizeVariants[size].input}
            disabled={disabled}
          />
        )}
        <CommandList className="max-h-[300px]">
          <CommandEmpty>{emptyMessage}</CommandEmpty>
          {renderCommandContent(groups, onItemSelect, size)}
        </CommandList>
      </CommandPrimitive>
    </div>
  )
}

// Search-only Command Implementation
const SearchOnlyCommand: React.FC<Extract<CommandProps, { variant: 'search-only' }>> = ({
  placeholder = "Search...",
  onSearch,
  onSubmit,
  loading = false,
  size = 'md',
  className,
  disabled
}) => {
  const [query, setQuery] = React.useState("")

  const handleValueChange = (value: string) => {
    setQuery(value)
    if (onSearch) onSearch(value)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && onSubmit) {
      onSubmit(query)
    }
  }

  return (
    <div className={cn("relative", className)}>
      <CommandPrimitive>
        <CommandInput
          placeholder={placeholder}
          value={query}
          onValueChange={handleValueChange}
          onKeyDown={handleKeyDown}
          className={cn(
            sizeVariants[size].input,
            loading && "pr-10"
          )}
          disabled={disabled}
        />
      </CommandPrimitive>
      {loading && (
        <div className="absolute right-3 top-1/2 -translate-y-1/2">
          <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
        </div>
      )}
    </div>
  )
}

// Main Command Component
export const Command: React.FC<CommandProps> = (props) => {
  switch (props.variant) {
    case 'dialog':
      return <DialogCommand {...props} />
    case 'combobox':
      return <ComboboxCommand {...props} />
    case 'dropdown':
      return <DropdownCommand {...props} />
    case 'inline':
      return <InlineCommand {...props} />
    case 'search-only':
      return <SearchOnlyCommand {...props} />
    default:
      return <DefaultCommand {...props} />
  }
}

Command.displayName = "Command"

export default Command