import type { <PERSON><PERSON>, StoryObj } from '@storybook/react-vite';
import { DatePicker, type DateRange } from './DatePicker';
import { useState } from 'react';

const meta: Meta<typeof DatePicker> = {
  title: 'Common/DatePicker',
  component: DatePicker,
  parameters: {
    docs: {
      description: {
        component: `
# DatePicker Component

A unified DatePicker component that consolidates **ALL** shadcn/ui date picker variants found in their official documentation into a single, flexible component.

## Consolidated shadcn/ui Variants

This component replaces multiple shadcn/ui date picker patterns:

- **\`variant="default"\`** → Basic date picker with popover (standard example)
- **\`variant="range"\`** → Date range picker 
- **\`variant="multiple"\`** → Multiple date selection
- **\`variant="with-presets"\`** → Date picker with preset options (Today, Last 7 days, etc.)
- **\`variant="form"\`** → Form-styled with label and proper form integration
- **\`variant="dropdown-years"\`** → With dropdown year/month selection (Calendar24 pattern)
- **\`variant="time"\`** → Date and time picker combined
- **\`variant="advanced"\`** → With text input support (Calendar29 pattern)
- **\`variant="inline"\`** → Always visible calendar
- **\`variant="input-only"\`** → Native HTML date input

## Key Features

- **Real shadcn/ui Patterns** - Each variant based on actual documentation examples
- **Smart Defaults** - Automatically configures appropriate modes and behaviors
- **Preset Support** - Built-in common presets (Today, Last 7 days, This month, etc.)
- **Advanced Features** - Text input parsing, time selection, dropdown navigation
- **Full Accessibility** - Complete ARIA support and keyboard navigation
- **Theme Compatible** - Works with light/dark modes and CSS variables

## Usage

\`\`\`tsx
// Basic date picker (most common shadcn/ui pattern)
<DatePicker 
  variant="default"
  value={date}
  onChange={setDate}
  placeholder="Pick a date"
/>

// Range with presets (popular advanced pattern)
<DatePicker
  variant="with-presets"
  value={dateRange}
  onChange={setDateRange}
  placeholder="Select date range"
/>

// Form integration
<DatePicker
  variant="form"
  aria-label="Birth Date"
  value={date}
  onChange={setDate}
/>

// Advanced with text input (natural language)
<DatePicker
  variant="advanced"
  enableTextInput
  value={date}
  onChange={setDate}
/>
\`\`\`
        `,
      },
    },
    layout: 'centered',
  },
  argTypes: {
    variant: {
      control: 'select',
      options: [
        'default',
        'range', 
        'multiple',
        'with-presets',
        'form',
        'dropdown-years',
        'time',
        'advanced',
        'inline',
        'input-only'
      ],
      description: 'Date picker variant - consolidates all shadcn/ui date picker patterns',
      table: {
        type: { summary: "'default' | 'range' | 'multiple' | 'with-presets' | 'form' | 'dropdown-years' | 'time' | 'advanced' | 'inline' | 'input-only'" },
        defaultValue: { summary: "'default'" },
      },
    },
    size: {
      control: 'select',
      options: ['sm', 'md', 'lg'],
      description: 'Size of the date picker input',
      table: {
        type: { summary: "'sm' | 'md' | 'lg'" },
        defaultValue: { summary: "'md'" },
      },
    },
    disabled: {
      control: 'boolean',
      description: 'Whether the date picker is disabled',
    },
    placeholder: {
      control: 'text',
      description: 'Placeholder text for the input trigger',
    },
    enableTextInput: {
      control: 'boolean',
      description: 'Enable natural language text input (for advanced variant)',
      if: { arg: 'variant', eq: 'advanced' },
    },
    timeFormat: {
      control: 'select',
      options: ['12h', '24h'],
      description: 'Time format for time variant',
      if: { arg: 'variant', eq: 'time' },
    },
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

// Default Story
export const Default: Story = {
  args: {
    variant: 'default',
    placeholder: 'Pick a date',
    size: 'md',
  },
  render: (args) => {
    const [date, setDate] = useState<Date>();
    return (
      <div className="w-80">
        <DatePicker
          {...args}
          value={date}
          onChange={(date) => setDate(date as Date)}
        />
      </div>
    );
  },
};

// All Variants - Shows ALL real shadcn/ui patterns
export const AllVariants: Story = {
  render: () => {
    const [defaultDate, setDefaultDate] = useState<Date>();
    const [rangeDate, setRangeDate] = useState<DateRange>({ from: undefined, to: undefined });
    const [multipleDates, setMultipleDates] = useState<Date[]>([]);
    const [presetDate, setPresetDate] = useState<DateRange>({ from: undefined, to: undefined });
    const [formDate, setFormDate] = useState<Date>();
    const [dropdownDate, setDropdownDate] = useState<Date>();
    const [timeDate, setTimeDate] = useState<Date>();
    const [advancedDate, setAdvancedDate] = useState<Date>();

    return (
      <div className="space-y-8 p-6 max-w-6xl">
        <div>
          <h2 className="text-2xl font-bold mb-2">All shadcn/ui DatePicker Variants</h2>
          <p className="text-muted-foreground mb-6">
            Every variant is based on real patterns from shadcn/ui documentation
          </p>
        </div>

        <div className="grid gap-8 lg:grid-cols-2 xl:grid-cols-3">
          {/* Default Pattern */}
          <div className="space-y-3">
            <h3 className="font-semibold">Default</h3>
            <DatePicker
              variant="default"
              value={defaultDate}
              onChange={(date) => setDefaultDate(date as Date)}
              placeholder="Pick a date"
            />
            <p className="text-xs text-muted-foreground">
              Standard shadcn/ui date picker pattern
            </p>
          </div>

          {/* Range Pattern */}
          <div className="space-y-3">
            <h3 className="font-semibold">Range</h3>
            <DatePicker
              variant="range"
              value={rangeDate}
              onChange={(range) => setRangeDate(range as DateRange)}
              placeholder="Select date range"
            />
            <p className="text-xs text-muted-foreground">
              Date range selection with dual calendar
            </p>
          </div>

          {/* Multiple Pattern */}
          <div className="space-y-3">
            <h3 className="font-semibold">Multiple</h3>
            <DatePicker
              variant="multiple"
              value={multipleDates}
              onChange={(dates) => setMultipleDates(dates as Date[])}
              placeholder="Select multiple dates"
            />
            <p className="text-xs text-muted-foreground">
              Multiple individual date selection
            </p>
          </div>

          {/* With Presets Pattern */}
          <div className="space-y-3">
            <h3 className="font-semibold">With Presets</h3>
            <DatePicker
              variant="with-presets"
              value={presetDate}
              onChange={(range) => setPresetDate(range as DateRange)}
              placeholder="Select date range"
            />
            <p className="text-xs text-muted-foreground">
              Range picker with common presets sidebar
            </p>
          </div>

          {/* Form Pattern */}
          <div className="space-y-3">
            <h3 className="font-semibold">Form</h3>
            <DatePicker
              variant="form"
              value={formDate}
              onChange={(date) => setFormDate(date as Date)}
              aria-label="Birth Date"
              placeholder="Select your birth date"
            />
            <p className="text-xs text-muted-foreground">
              Form-styled with proper label integration
            </p>
          </div>

          {/* Dropdown Years Pattern */}
          <div className="space-y-3">
            <h3 className="font-semibold">Dropdown Years</h3>
            <DatePicker
              variant="dropdown-years"
              value={dropdownDate}
              onChange={(date) => setDropdownDate(date as Date)}
              placeholder="Select date"
            />
            <p className="text-xs text-muted-foreground">
              Calendar24 pattern - with year/month dropdowns
            </p>
          </div>

          {/* Time Pattern */}
          <div className="space-y-3">
            <h3 className="font-semibold">Date & Time</h3>
            <DatePicker
              variant="time"
              value={timeDate}
              onChange={(date) => setTimeDate(date as Date)}
              timeFormat="12h"
              placeholder="Select date and time"
            />
            <p className="text-xs text-muted-foreground">
              Combined date and time selection
            </p>
          </div>

          {/* Advanced Pattern */}
          <div className="space-y-3">
            <h3 className="font-semibold">Advanced</h3>
            <DatePicker
              variant="advanced"
              value={advancedDate}
              onChange={(date) => setAdvancedDate(date as Date)}
              enableTextInput
              placeholder="Select or type date"
            />
            <p className="text-xs text-muted-foreground">
              Calendar29 pattern - with text input support
            </p>
          </div>

          {/* Input Only Pattern */}
          <div className="space-y-3">
            <h3 className="font-semibold">Input Only</h3>
            <DatePicker
              variant="input-only"
              value={defaultDate}
              onChange={(date) => setDefaultDate(date as Date)}
            />
            <p className="text-xs text-muted-foreground">
              Native HTML date input
            </p>
          </div>
        </div>

        {/* Inline Calendar */}
        <div className="space-y-3">
          <h3 className="font-semibold">Inline Calendar</h3>
          <DatePicker
            variant="inline"
            value={defaultDate}
            onChange={(date) => setDefaultDate(date as Date)}
            className="border rounded-lg inline-block"
          />
          <p className="text-xs text-muted-foreground">
            Always visible calendar component
          </p>
        </div>

        {/* Current Selections */}
        <div className="mt-8 p-4 bg-muted rounded-lg">
          <h4 className="font-semibold mb-2">Current Selections:</h4>
          <div className="grid gap-2 text-sm md:grid-cols-2">
            <p><strong>Default:</strong> {defaultDate?.toLocaleDateString() || 'None'}</p>
            <p><strong>Range:</strong> {
              rangeDate.from && rangeDate.to 
                ? `${rangeDate.from.toLocaleDateString()} - ${rangeDate.to.toLocaleDateString()}`
                : rangeDate.from 
                  ? `${rangeDate.from.toLocaleDateString()} - ...`
                  : 'None'
            }</p>
            <p><strong>Multiple:</strong> {
              multipleDates.length > 0 
                ? `${multipleDates.length} dates selected`
                : 'None'
            }</p>
            <p><strong>With Presets:</strong> {
              presetDate.from && presetDate.to 
                ? `${presetDate.from.toLocaleDateString()} - ${presetDate.to.toLocaleDateString()}`
                : 'None'
            }</p>
            <p><strong>Form:</strong> {formDate?.toLocaleDateString() || 'None'}</p>
            <p><strong>Dropdown:</strong> {dropdownDate?.toLocaleDateString() || 'None'}</p>
            <p><strong>Time:</strong> {timeDate?.toLocaleString() || 'None'}</p>
            <p><strong>Advanced:</strong> {advancedDate?.toLocaleDateString() || 'None'}</p>
          </div>
        </div>
      </div>
    );
  },
  parameters: {
    layout: 'fullscreen',
  },
};

// Size Variants
export const SizeVariants: Story = {
  render: () => {
    const [date, setDate] = useState<Date>();

    return (
      <div className="space-y-6 p-6 w-80">
        <h3 className="text-lg font-semibold">Size Variants</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">Small</label>
            <DatePicker
              variant="default"
              size="sm"
              value={date}
              onChange={(date) => setDate(date as Date)}
              placeholder="Small picker"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">Medium (Default)</label>
            <DatePicker
              variant="default"
              size="md"
              value={date}
              onChange={(date) => setDate(date as Date)}
              placeholder="Medium picker"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">Large</label>
            <DatePicker
              variant="default"
              size="lg"
              value={date}
              onChange={(date) => setDate(date as Date)}
              placeholder="Large picker"
            />
          </div>
        </div>
      </div>
    );
  },
};

// Presets Showcase
export const PresetsShowcase: Story = {
  render: () => {
    const [dateRange, setDateRange] = useState<DateRange>({ from: undefined, to: undefined });

    // Custom presets example
    const customPresets = [
      {
        label: "Today",
        value: { from: new Date(), to: new Date() }
      },
      {
        label: "Tomorrow", 
        value: { 
          from: new Date(Date.now() + 24 * 60 * 60 * 1000),
          to: new Date(Date.now() + 24 * 60 * 60 * 1000)
        }
      },
      {
        label: "This Week",
        value: {
          from: new Date(Date.now() - new Date().getDay() * 24 * 60 * 60 * 1000),
          to: new Date(Date.now() + (6 - new Date().getDay()) * 24 * 60 * 60 * 1000)
        }
      },
      {
        label: "Next 30 Days",
        value: {
          from: new Date(),
          to: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
        }
      },
      {
        label: "Q1 2024",
        value: {
          from: new Date(2024, 0, 1),
          to: new Date(2024, 2, 31)
        }
      }
    ];

    return (
      <div className="space-y-6 p-6 max-w-2xl">
        <div>
          <h3 className="text-lg font-semibold mb-4">Preset Options</h3>
          <p className="text-muted-foreground mb-4">
            The preset variant comes with built-in common presets, but you can also provide custom ones.
          </p>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">Default Presets</label>
            <DatePicker
              variant="with-presets"
              value={dateRange}
              onChange={(range) => setDateRange(range as DateRange)}
              placeholder="Select date range"
            />
            <p className="text-sm text-muted-foreground mt-1">
              Includes: Today, Yesterday, Last 7 days, Last 30 days, This month, Last month
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Custom Presets</label>
            <DatePicker
              variant="with-presets"
              value={dateRange}
              onChange={(range) => setDateRange(range as DateRange)}
              placeholder="Select date range"
              presets={customPresets}
            />
            <p className="text-sm text-muted-foreground mt-1">
              Custom business-specific presets
            </p>
          </div>
        </div>

        <div className="p-4 bg-muted rounded-lg">
          <h4 className="font-medium mb-2">Selected Range:</h4>
          <p className="text-sm">
            {dateRange.from && dateRange.to 
              ? `${dateRange.from.toLocaleDateString()} - ${dateRange.to.toLocaleDateString()}`
              : dateRange.from 
                ? `${dateRange.from.toLocaleDateString()} - ...`
                : 'None'
            }
          </p>
        </div>
      </div>
    );
  },
};

// Advanced Features
export const AdvancedFeatures: Story = {
  render: () => {
    const [advancedDate, setAdvancedDate] = useState<Date>();
    const [timeDate, setTimeDate] = useState<Date>();
    const [constrainedDate, setConstrainedDate] = useState<Date>();

    const today = new Date();
    const futureOnly = new Date(today);
    futureOnly.setDate(today.getDate() + 1);
    const maxDate = new Date(today);
    maxDate.setMonth(today.getMonth() + 6);

    return (
      <div className="space-y-6 p-6 max-w-2xl">
        <div>
          <h3 className="text-lg font-semibold mb-4">Advanced Features</h3>
          <p className="text-muted-foreground mb-4">
            Showcase of advanced date picker capabilities.
          </p>
        </div>

        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium mb-2">
              Natural Language Input
            </label>
            <DatePicker
              variant="advanced"
              value={advancedDate}
              onChange={(date) => setAdvancedDate(date as Date)}
              enableTextInput
              placeholder="Try typing 'tomorrow' or 'next Friday'"
            />
            <p className="text-sm text-muted-foreground mt-1">
              Type natural language dates in the text input above the calendar
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              Date and Time Selection
            </label>
            <DatePicker
              variant="time"
              value={timeDate}
              onChange={(date) => setTimeDate(date as Date)}
              timeFormat="12h"
              placeholder="Select date and time"
            />
            <p className="text-sm text-muted-foreground mt-1">
              Selected: {timeDate ? timeDate.toLocaleString() : 'None'}
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              Date Constraints
            </label>
            <DatePicker
              variant="default"
              value={constrainedDate}
              onChange={(date) => setConstrainedDate(date as Date)}
              fromDate={futureOnly}
              toDate={maxDate}
              disabledDates={(date) => date.getDay() === 0 || date.getDay() === 6}
              placeholder="Future weekdays only"
            />
            <p className="text-sm text-muted-foreground mt-1">
              Only future weekdays, within 6 months
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              Year/Month Dropdown Navigation
            </label>
            <DatePicker
              variant="dropdown-years"
              value={constrainedDate}
              onChange={(date) => setConstrainedDate(date as Date)}
              placeholder="Navigate with dropdowns"
            />
            <p className="text-sm text-muted-foreground mt-1">
              Easy navigation to distant dates using year/month dropdowns
            </p>
          </div>
        </div>
      </div>
    );
  },
};

// Accessibility Demo
export const Accessibility: Story = {
  render: () => {
    const [date, setDate] = useState<Date>();

    return (
      <div className="space-y-6 p-6 max-w-2xl">
        <div>
          <h3 className="text-lg font-semibold mb-4">Accessibility Features</h3>
          <p className="text-muted-foreground mb-4">
            All variants include full accessibility support with proper ARIA attributes and keyboard navigation.
          </p>
        </div>

        <div className="space-y-4">
          <div>
            <DatePicker
              variant="form"
              value={date}
              onChange={(date) => setDate(date as Date)}
              aria-label="Appointment Date"
              aria-describedby="appointment-help"
              placeholder="Select appointment date"
            />
            <p id="appointment-help" className="text-sm text-muted-foreground mt-1">
              Use Tab to focus, Enter/Space to open calendar, arrow keys to navigate dates
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Disabled State</label>
            <DatePicker
              variant="default"
              disabled
              placeholder="Disabled date picker"
            />
          </div>
        </div>

        <div className="bg-muted p-4 rounded-lg">
          <h4 className="font-medium mb-2">Keyboard Navigation:</h4>
          <ul className="text-sm space-y-1">
            <li>• <kbd className="px-1 py-0.5 bg-background border rounded text-xs">Tab</kbd> - Focus date picker</li>
            <li>• <kbd className="px-1 py-0.5 bg-background border rounded text-xs">Enter</kbd>/<kbd className="px-1 py-0.5 bg-background border rounded text-xs">Space</kbd> - Open calendar</li>
            <li>• <kbd className="px-1 py-0.5 bg-background border rounded text-xs">↑↓←→</kbd> - Navigate calendar dates</li>
            <li>• <kbd className="px-1 py-0.5 bg-background border rounded text-xs">Escape</kbd> - Close calendar</li>
            <li>• <kbd className="px-1 py-0.5 bg-background border rounded text-xs">PageUp/PageDown</kbd> - Change months</li>
            <li>• <kbd className="px-1 py-0.5 bg-background border rounded text-xs">Home/End</kbd> - First/last day of week</li>
          </ul>
        </div>
      </div>
    );
  },
};

// Form Integration
export const FormIntegration: Story = {
  render: () => {
    const [startDate, setStartDate] = useState<Date>();
    const [endDate, setEndDate] = useState<Date>();
    const [eventDate, setEventDate] = useState<Date>();
    const [reminderTimes, setReminderTimes] = useState<Date[]>([]);

    return (
      <div className="space-y-8 p-6 max-w-2xl">
        <div>
          <h3 className="text-lg font-semibold mb-4">Form Integration Examples</h3>
          <p className="text-muted-foreground mb-4">
            Real-world examples of using DatePicker in forms and applications.
          </p>
        </div>

        {/* Event Form */}
        <div className="bg-white p-6 border rounded-lg">
          <h4 className="font-medium mb-4">Event Planning Form</h4>
          <form className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Event Name</label>
              <input
                type="text"
                placeholder="Enter event name"
                className="w-full px-3 py-2 border border-input rounded-md"
              />
            </div>
            
            <div className="grid gap-4 md:grid-cols-2">
              <DatePicker
                variant="form"
                value={eventDate}
                onChange={(date) => setEventDate(date as Date)}
                aria-label="Event Date"
                placeholder="Select event date"
                fromDate={new Date()}
              />
              
              <DatePicker
                variant="time"
                value={eventDate}
                onChange={(date) => setEventDate(date as Date)}
                placeholder="Event time"
              />
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <DatePicker
                variant="form"
                value={startDate}
                onChange={(date) => setStartDate(date as Date)}
                aria-label="Registration Start"
                placeholder="Registration opens"
                size="sm"
              />
              
              <DatePicker
                variant="form"
                value={endDate}
                onChange={(date) => setEndDate(date as Date)}
                aria-label="Registration End"
                placeholder="Registration closes"
                size="sm"
                fromDate={startDate}
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Reminder Dates</label>
              <DatePicker
                variant="multiple"
                value={reminderTimes}
                onChange={(dates) => setReminderTimes(dates as Date[])}
                placeholder="Add reminder dates"
                fromDate={new Date()}
              />
            </div>
          </form>
        </div>

        {/* Dashboard Filter */}
        <div className="bg-white p-6 border rounded-lg">
          <h4 className="font-medium mb-4">Dashboard Date Filter</h4>
          <div className="flex flex-wrap gap-4 items-end">
            <div className="flex-1 min-w-48">
              <DatePicker
                variant="with-presets"
                placeholder="Filter by date range"
              />
            </div>
            <button className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90">
              Apply Filter
            </button>
          </div>
        </div>

        {/* Quick Date Selection */}
        <div className="bg-white p-6 border rounded-lg">
          <h4 className="font-medium mb-4">Quick Actions</h4>
          <div className="grid gap-3 sm:grid-cols-3">
            <DatePicker
              variant="default"
              size="sm"
              placeholder="Start date"
            />
            <DatePicker
              variant="default"
              size="sm"
              placeholder="Due date"
            />
            <DatePicker
              variant="advanced"
              size="sm"
              enableTextInput
              placeholder="Flexible date"
            />
          </div>
        </div>
      </div>
    );
  },
  parameters: {
    layout: 'fullscreen',
  },
};