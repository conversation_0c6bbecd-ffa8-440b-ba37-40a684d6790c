import type { <PERSON><PERSON>, StoryObj } from '@storybook/react-vite';
import { useState } from 'react';
import { Combobox } from './Combobox';
import type { ComboboxOption, ComboboxGroup, } from './types';
import { createOption, createGroup} from './types';
import { User, Settings, Mail, Calendar, Star, Globe, Code, Heart } from 'lucide-react';

const meta: Meta<typeof Combobox> = {
  title: 'Components/Combobox',
  component: Combobox,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A unified combobox component that consolidates all shadcn/ui combobox patterns into a single component with variants. Supports single/multi-select, search, creation, icons, grouping, and responsive behavior.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: [
        'default',
        'responsive', 
        'form',
        'dropdown-menu',
        'searchable',
        'multi-select',
        'creatable',
        'with-icons',
        'grouped'
      ],
      description: 'The variant of the combobox component',
    },
    size: {
      control: 'select',
      options: ['sm', 'md', 'lg'],
      description: 'Size of the component',
    },
    disabled: {
      control: 'boolean',
      description: 'Whether the combobox is disabled',
    },
    searchable: {
      control: 'boolean',
      description: 'Whether to show the search input',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

const frameworks: ComboboxOption[] = [
  createOption('next.js', 'Next.js'),
  createOption('react', 'React'),
  createOption('vue', 'Vue.js'),
  createOption('angular', 'Angular'),
  createOption('svelte', 'Svelte'),
  createOption('solid', 'SolidJS'),
];

const countries: ComboboxOption[] = [
  createOption('us', 'United States'),
  createOption('ca', 'Canada'),
  createOption('uk', 'United Kingdom'),
  createOption('de', 'Germany'),
  createOption('fr', 'France'),
  createOption('jp', 'Japan'),
  createOption('au', 'Australia'),
];

const iconsFrameworks: ComboboxOption[] = [
  createOption('next.js', 'Next.js', { icon: <Code className="w-4 h-4" /> }),
  createOption('react', 'React', { icon: <Star className="w-4 h-4" /> }),
  createOption('vue', 'Vue.js', { icon: <Globe className="w-4 h-4" /> }),
  createOption('angular', 'Angular', { icon: <Settings className="w-4 h-4" /> }),
];

const detailedOptions: ComboboxOption[] = [
  createOption('user', 'User Management', { 
    icon: <User className="w-4 h-4" />,
    description: 'Manage user accounts and permissions'
  }),
  createOption('settings', 'System Settings', { 
    icon: <Settings className="w-4 h-4" />,
    description: 'Configure system preferences'
  }),
  createOption('mail', 'Email Templates', { 
    icon: <Mail className="w-4 h-4" />,
    description: 'Create and manage email templates'
  }),
  createOption('calendar', 'Calendar Events', { 
    icon: <Calendar className="w-4 h-4" />,
    description: 'Schedule and manage events'
  }),
];

const groupedOptions: ComboboxGroup[] = [
  createGroup('Frontend Frameworks', [
    createOption('react', 'React'),
    createOption('vue', 'Vue.js'),
    createOption('angular', 'Angular'),
    createOption('svelte', 'Svelte'),
  ]),
  createGroup('Backend Frameworks', [
    createOption('express', 'Express.js'),
    createOption('fastify', 'Fastify'),
    createOption('nestjs', 'NestJS'),
    createOption('koa', 'Koa.js'),
  ]),
  createGroup('Full-Stack Frameworks', [
    createOption('next.js', 'Next.js'),
    createOption('nuxt', 'Nuxt.js'),
    createOption('sveltekit', 'SvelteKit'),
    createOption('remix', 'Remix'),
  ]),
];

export const Default: Story = {
  args: {
    placeholder: 'Select framework...',
    options: frameworks,
  },
};

export const AllVariants: Story = {
  render: () => {
    const [values, setValues] = useState<Record<string, any>>({
      default: '',
      responsive: '',
      form: '',
      dropdownMenu: '',
      searchable: '',
      multiSelect: [],
      creatable: '',
      withIcons: '',
      grouped: '',
    });

    const [creatableOptions, setCreatableOptions] = useState([...frameworks]);

    const handleValueChange = (variant: string) => (value: any) => {
      setValues(prev => ({ ...prev, [variant]: value }));
    };

    const handleCreateOption = (newValue: string) => {
      const newOption = createOption(newValue.toLowerCase().replace(/\s+/g, '-'), newValue);
      setCreatableOptions(prev => [...prev, newOption]);
      setValues(prev => ({ ...prev, creatable: newOption.value }));
    };

    return (
      <div className="grid gap-8 w-full max-w-2xl">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">All Combobox Variants</h2>
          <p className="text-muted-foreground">
            Comprehensive showcase of all available combobox patterns
          </p>
        </div>

        <div className="grid gap-6">
          <div className="space-y-2">
            <h3 className="text-lg font-semibold">Default</h3>
            <p className="text-sm text-muted-foreground">Basic combobox with popover</p>
            <Combobox
              variant="default"
              placeholder="Select framework..."
              options={frameworks}
              value={values.default}
              onValueChange={handleValueChange('default')}
            />
          </div>

          <div className="space-y-2">
            <h3 className="text-lg font-semibold">Responsive</h3>
            <p className="text-sm text-muted-foreground">Uses drawer on mobile, popover on desktop</p>
            <Combobox
              variant="responsive"
              placeholder="Select country..."
              options={countries}
              value={values.responsive}
              onValueChange={handleValueChange('responsive')}
            />
          </div>

          <div className="space-y-2">
            <h3 className="text-lg font-semibold">Form</h3>
            <p className="text-sm text-muted-foreground">Full-width for forms with react-hook-form integration</p>
            <Combobox
              variant="form"
              placeholder="Select for form..."
              options={frameworks}
              value={values.form}
              onValueChange={handleValueChange('form')}
            />
          </div>

          <div className="space-y-2">
            <h3 className="text-lg font-semibold">Dropdown Menu</h3>
            <p className="text-sm text-muted-foreground">Styled as dropdown menu with optional label</p>
            <Combobox
              variant="dropdown-menu"
              placeholder="Select option..."
              options={frameworks}
              value={values.dropdownMenu}
              onValueChange={handleValueChange('dropdownMenu')}
              menuLabel="Choose Framework"
            />
          </div>

          <div className="space-y-2">
            <h3 className="text-lg font-semibold">Multi-Select</h3>
            <p className="text-sm text-muted-foreground">Allows multiple selection with count display</p>
            <Combobox
              variant="multi-select"
              placeholder="Select multiple frameworks..."
              options={frameworks}
              value={values.multiSelect}
              onValueChange={handleValueChange('multiSelect')}
              maxSelected={3}
            />
          </div>

          <div className="space-y-2">
            <h3 className="text-lg font-semibold">Creatable</h3>
            <p className="text-sm text-muted-foreground">Allows creating new options on the fly</p>
            <Combobox
              variant="creatable"
              placeholder="Select or create framework..."
              options={creatableOptions}
              value={values.creatable}
              onValueChange={handleValueChange('creatable')}
              onCreateOption={handleCreateOption}
            />
          </div>

          <div className="space-y-2">
            <h3 className="text-lg font-semibold">With Icons</h3>
            <p className="text-sm text-muted-foreground">Displays icons alongside option labels</p>
            <Combobox
              variant="with-icons"
              placeholder="Select with icon..."
              options={iconsFrameworks}
              value={values.withIcons}
              onValueChange={handleValueChange('withIcons')}
            />
          </div>

          <div className="space-y-2">
            <h3 className="text-lg font-semibold">Grouped</h3>
            <p className="text-sm text-muted-foreground">Groups options under category headings</p>
            <Combobox
              variant="grouped"
              placeholder="Select from groups..."
              groups={groupedOptions}
              value={values.grouped}
              onValueChange={handleValueChange('grouped')}
            />
          </div>
        </div>
      </div>
    );
  },
};

export const Interactive: Story = {
  render: () => {
    const [value, setValue] = useState('');
    const [multiValue, setMultiValue] = useState<string[]>([]);

    return (
      <div className="space-y-8 w-full max-w-md">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Single Selection</h3>
          <Combobox
            placeholder="Try selecting a framework..."
            options={detailedOptions}
            value={value}
            onValueChange={(val) => setValue(val as string)}
            variant="with-icons"
          />
          {value && (
            <p className="text-sm text-muted-foreground">
              Selected: <span className="font-medium">{detailedOptions.find(opt => opt.value === value)?.label}</span>
            </p>
          )}
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Multi Selection</h3>
          <Combobox
            placeholder="Select multiple options..."
            options={frameworks}
            value={multiValue}
            onValueChange={(val) => setMultiValue(val as string[])}
            variant="multi-select"
            maxSelected={4}
          />
          {multiValue.length > 0 && (
            <div className="text-sm text-muted-foreground">
              Selected ({multiValue.length}): 
              <span className="font-medium ml-1">
                {multiValue.map(v => frameworks.find(opt => opt.value === v)?.label).join(', ')}
              </span>
            </div>
          )}
        </div>
      </div>
    );
  },
};

export const Accessibility: Story = {
  render: () => (
    <div className="space-y-8 w-full max-w-md">
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Keyboard Navigation</h3>
        <p className="text-sm text-muted-foreground">
          Use Tab to focus, Space/Enter to open, Arrow keys to navigate, Enter to select, Escape to close.
        </p>
        <Combobox
          placeholder="Test keyboard navigation..."
          options={frameworks}
          searchPlaceholder="Type to search frameworks..."
        />
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Disabled State</h3>
        <Combobox
          placeholder="This combobox is disabled"
          options={frameworks}
          disabled
        />
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">With Screen Reader Support</h3>
        <p className="text-sm text-muted-foreground">
          Proper ARIA attributes and roles for screen reader compatibility.
        </p>
        <Combobox
          placeholder="Screen reader friendly..."
          options={detailedOptions}
          variant="with-icons"
        />
      </div>
    </div>
  ),
};

export const Sizes: Story = {
  render: () => (
    <div className="space-y-6 w-full max-w-md">
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Small</h3>
        <Combobox
          size="sm"
          placeholder="Small combobox..."
          options={frameworks.slice(0, 3)}
        />
      </div>

      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Medium (Default)</h3>
        <Combobox
          size="md"
          placeholder="Medium combobox..."
          options={frameworks.slice(0, 3)}
        />
      </div>

      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Large</h3>
        <Combobox
          size="lg"
          placeholder="Large combobox..."
          options={frameworks.slice(0, 3)}
        />
      </div>
    </div>
  ),
};

export const AdvancedFeatures: Story = {
  render: () => {
    const [creatableValue, setCreatableValue] = useState('');
    const [options, setOptions] = useState([...frameworks]);

    const handleCreate = (newValue: string) => {
      const newOption = createOption(
        newValue.toLowerCase().replace(/\s+/g, '-'), 
        newValue,
        { icon: <Heart className="w-4 h-4" /> }
      );
      setOptions(prev => [...prev, newOption]);
      setCreatableValue(newOption.value);
    };

    return (
      <div className="space-y-8 w-full max-w-lg">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Create New Options</h3>
          <p className="text-sm text-muted-foreground">
            Type a new framework name and press Enter or click Create to add it.
          </p>
          <Combobox
            variant="creatable"
            placeholder="Type to create new option..."
            options={options}
            value={creatableValue}
            onValueChange={(val) => setCreatableValue(val as string)}
            onCreateOption={handleCreate}
            createOptionText="Add"
          />
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Non-searchable</h3>
          <p className="text-sm text-muted-foreground">
            Combobox without search functionality.
          </p>
          <Combobox
            placeholder="Browse options..."
            options={frameworks}
            searchable={false}
          />
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Custom No Results</h3>
          <Combobox
            placeholder="Search for something..."
            options={frameworks}
            noResultsText="No frameworks match your search. Try 'React' or 'Vue'."
            searchPlaceholder="Search frameworks..."
          />
        </div>
      </div>
    );
  },
};

export const FormIntegration: Story = {
  render: () => {
    const [formValues, setFormValues] = useState({
      framework: '',
      countries: [] as string[],
      category: '',
    });

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();
      alert(`Form submitted with values: ${JSON.stringify(formValues, null, 2)}`);
    };

    return (
      <form onSubmit={handleSubmit} className="space-y-6 w-full max-w-md">
        <h3 className="text-lg font-semibold">Form Integration Example</h3>
        
        <div className="space-y-2">
          <label className="text-sm font-medium">Preferred Framework</label>
          <Combobox
            variant="form"
            placeholder="Select framework..."
            options={frameworks}
            value={formValues.framework}
            onValueChange={(val) => setFormValues(prev => ({ ...prev, framework: val as string }))}
          />
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">Target Countries</label>
          <Combobox
            variant="multi-select"
            placeholder="Select countries..."
            options={countries}
            value={formValues.countries}
            onValueChange={(val) => setFormValues(prev => ({ ...prev, countries: val as string[] }))}
            maxSelected={3}
          />
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">Category</label>
          <Combobox
            variant="grouped"
            placeholder="Select category..."
            groups={groupedOptions}
            value={formValues.category}
            onValueChange={(val) => setFormValues(prev => ({ ...prev, category: val as string }))}
          />
        </div>

        <button
          type="submit"
          className="w-full bg-primary text-primary-foreground py-2 px-4 rounded-md hover:bg-primary/90 transition-colors"
        >
          Submit Form
        </button>

        <div className="text-xs text-muted-foreground">
          <pre>{JSON.stringify(formValues, null, 2)}</pre>
        </div>
      </form>
    );
  },
};