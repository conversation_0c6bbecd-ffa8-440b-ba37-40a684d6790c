import React, { useState, useEffect } from "react";
import { Check, ChevronsUpDown, Search, Plus, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Drawer,
  DrawerContent,
  DrawerTrigger,
} from "@/components/ui/drawer";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";


function useMediaQuery(query: string) {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    const media = window.matchMedia(query);
    if (media.matches !== matches) {
      setMatches(media.matches);
    }
    const listener = () => setMatches(media.matches);
    media.addListener(listener);
    return () => media.removeListener(listener);
  }, [matches, query]);

  return matches;
}

export interface ComboboxOption {
  value: string;
  label: string;
  disabled?: boolean;
  icon?: React.ReactNode;
  description?: string;
}

export interface ComboboxGroup {
  label: string;
  options: ComboboxOption[];
}

export interface ComboboxProps {
  variant?: 
    | "default" 
    | "responsive" 
    | "form" 
    | "dropdown-menu" 
    | "searchable" 
    | "multi-select"
    | "creatable"
    | "with-icons"
    | "grouped";
  
  value?: string | string[];
  onValueChange?: (value: string | string[]) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  
  options?: ComboboxOption[];
  groups?: ComboboxGroup[];
  
  searchPlaceholder?: string;
  noResultsText?: string;
  searchable?: boolean;
  
  onCreateOption?: (value: string) => void;
  createOptionText?: string;
  
  maxSelected?: number;
  showSelectedCount?: boolean;
  
  triggerClassName?: string;
  contentClassName?: string;
  size?: "sm" | "md" | "lg";
  
  mobileBreakpoint?: string;
  
  triggerIcon?: React.ReactNode;
  menuLabel?: string;
}

export const Combobox = React.forwardRef<HTMLButtonElement, ComboboxProps>(
  ({
    variant = "default",
    value,
    onValueChange,
    placeholder = "Select option...",
    disabled = false,
    className,
    options = [],
    groups = [],
    searchPlaceholder = "Search...",
    noResultsText = "No results found.",
    searchable = true,
    onCreateOption,
    createOptionText = "Create",
    maxSelected,
    showSelectedCount = true,
    triggerClassName,
    contentClassName,
    size = "md",
    mobileBreakpoint = "(max-width: 768px)",
    triggerIcon,
    menuLabel,
    ...props
  }, ref) => {
    const [open, setOpen] = useState(false);
    const [searchValue, setSearchValue] = useState("");
    const isMobile = useMediaQuery(mobileBreakpoint);
    
    const allOptions = groups.length > 0 
      ? groups.flatMap(group => group.options)
      : options;
    
    const isMultiSelect = variant === "multi-select";
    const selectedValues = isMultiSelect 
      ? (Array.isArray(value) ? value : value ? [value] : [])
      : (Array.isArray(value) ? value[0] : value);
    
    const handleSelect = (optionValue: string) => {
      if (isMultiSelect) {
        const currentValues = selectedValues as string[];
        const newValues = currentValues.includes(optionValue)
          ? currentValues.filter(v => v !== optionValue)
          : maxSelected && currentValues.length >= maxSelected
            ? currentValues
            : [...currentValues, optionValue];
        onValueChange?.(newValues);
      } else {
        const newValue = selectedValues === optionValue ? "" : optionValue;
        onValueChange?.(newValue);
        setOpen(false);
      }
    };
    
    const handleCreate = () => {
      if (searchValue && onCreateOption) {
        onCreateOption(searchValue);
        setSearchValue("");
        setOpen(false);
      }
    };
    
    const filteredOptions = allOptions.filter(option =>
      option.label.toLowerCase().includes(searchValue.toLowerCase())
    );
    
    const filteredGroups = groups.map(group => ({
      ...group,
      options: group.options.filter(option =>
        option.label.toLowerCase().includes(searchValue.toLowerCase())
      )
    })).filter(group => group.options.length > 0);
    
    const sizeStyles = {
      sm: "h-8 text-xs",
      md: "h-9 text-sm",
      lg: "h-10 text-base"
    };
    
    const getDisplayText = () => {
      if (isMultiSelect) {
        const values = selectedValues as string[];
        if (values.length === 0) return placeholder;
        if (showSelectedCount && values.length > 1) {
          return `${values.length} selected`;
        }
        return values
          .map(v => allOptions.find(opt => opt.value === v)?.label)
          .filter(Boolean)
          .join(", ");
      } else {
        const selected = allOptions.find(opt => opt.value === selectedValues);
        return selected ? selected.label : placeholder;
      }
    };
    
    const CommandContent = () => (
      <Command shouldFilter={false}>
        {searchable && (
          <CommandInput 
            placeholder={searchPlaceholder} 
            value={searchValue}
            onValueChange={setSearchValue}
          />
        )}
        <CommandList>
          <CommandEmpty>
            <div className="py-2 text-center">
              <p className="text-sm text-muted-foreground">{noResultsText}</p>
              {variant === "creatable" && searchValue && onCreateOption && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="mt-2"
                  onClick={handleCreate}
                >
                  <Plus className="w-4 h-4 mr-2" />
                  {createOptionText} "{searchValue}"
                </Button>
              )}
            </div>
          </CommandEmpty>
          
          {variant === "creatable" && searchValue && !filteredOptions.some(opt => 
            opt.label.toLowerCase() === searchValue.toLowerCase()
          ) && (
            <CommandGroup>
              <CommandItem onSelect={handleCreate}>
                <Plus className="w-4 h-4 mr-2" />
                {createOptionText} "{searchValue}"
              </CommandItem>
            </CommandGroup>
          )}
          
          {groups.length > 0 ? (
            filteredGroups.map((group) => (
              <CommandGroup key={group.label} heading={group.label}>
                {group.options.map((option) => (
                  <CommandItem
                    key={option.value}
                    value={option.value}
                    disabled={option.disabled}
                    onSelect={() => handleSelect(option.value)}
                  >
                    <div className="flex items-center justify-between w-full">
                      <div className="flex items-center">
                        {variant === "with-icons" && option.icon && (
                          <span className="w-4 h-4 mr-2">{option.icon}</span>
                        )}
                        <div>
                          <div>{option.label}</div>
                          {option.description && (
                            <div className="text-xs text-muted-foreground">
                              {option.description}
                            </div>
                          )}
                        </div>
                      </div>
                      <Check
                        className={cn(
                          "w-4 h-4",
                          isMultiSelect
                            ? (selectedValues as string[]).includes(option.value)
                              ? "opacity-100"
                              : "opacity-0"
                            : selectedValues === option.value
                              ? "opacity-100"
                              : "opacity-0"
                        )}
                      />
                    </div>
                  </CommandItem>
                ))}
              </CommandGroup>
            ))
          ) : (
            <CommandGroup>
              {filteredOptions.map((option) => (
                <CommandItem
                  key={option.value}
                  value={option.value}
                  disabled={option.disabled}
                  onSelect={() => handleSelect(option.value)}
                >
                  <div className="flex items-center justify-between w-full">
                    <div className="flex items-center">
                      {variant === "with-icons" && option.icon && (
                        <span className="w-4 h-4 mr-2">{option.icon}</span>
                      )}
                      <div>
                        <div>{option.label}</div>
                        {option.description && (
                          <div className="text-xs text-muted-foreground">
                            {option.description}
                          </div>
                        )}
                      </div>
                    </div>
                    <Check
                      className={cn(
                        "w-4 h-4",
                        isMultiSelect
                          ? (selectedValues as string[]).includes(option.value)
                            ? "opacity-100"
                            : "opacity-0"
                          : selectedValues === option.value
                            ? "opacity-100"
                            : "opacity-0"
                      )}
                    />
                  </div>
                </CommandItem>
              ))}
            </CommandGroup>
          )}
        </CommandList>
      </Command>
    );
    
    if (variant === "dropdown-menu") {
      return (
        <DropdownMenu open={open} onOpenChange={setOpen}>
          <DropdownMenuTrigger asChild>
            <Button
              ref={ref}
              variant="outline"
              disabled={disabled}
              className={cn(
                "justify-between",
                sizeStyles[size],
                triggerClassName,
                className
              )}
              {...props}
            >
              {getDisplayText()}
              {triggerIcon || <ChevronsUpDown className="w-4 h-4 ml-2 opacity-50" />}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className={cn("p-0", contentClassName)}>
            {menuLabel && (
              <>
                <DropdownMenuLabel>{menuLabel}</DropdownMenuLabel>
                <DropdownMenuSeparator />
              </>
            )}
            <div className="max-h-60 overflow-auto">
              <CommandContent />
            </div>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    }
    
    if (variant === "responsive" && isMobile) {
      return (
        <Drawer open={open} onOpenChange={setOpen}>
          <DrawerTrigger asChild>
            <Button
              ref={ref}
              variant="outline"
              disabled={disabled}
              className={cn(
                "w-full justify-between",
                sizeStyles[size],
                triggerClassName,
                className
              )}
              {...props}
            >
              {getDisplayText()}
              <ChevronsUpDown className="w-4 h-4 ml-2 opacity-50" />
            </Button>
          </DrawerTrigger>
          <DrawerContent className={contentClassName}>
            <div className="p-4">
              <CommandContent />
            </div>
          </DrawerContent>
        </Drawer>
      );
    }
    
    return (
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            ref={ref}
            variant="outline"
            role="combobox"
            aria-expanded={open}
            disabled={disabled}
            className={cn(
              "justify-between",
              variant === "form" && "w-full",
              sizeStyles[size],
              triggerClassName,
              className
            )}
            {...props}
          >
            <span className={cn(
              "truncate",
              !selectedValues && "text-muted-foreground"
            )}>
              {getDisplayText()}
            </span>
            <div className="flex items-center ml-2 gap-1">
              {isMultiSelect && (selectedValues as string[]).length > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-4 w-4 p-0 hover:bg-transparent"
                  onClick={(e) => {
                    e.stopPropagation();
                    onValueChange?.([]);
                  }}
                >
                  <X className="w-3 h-3" />
                </Button>
              )}
              <ChevronsUpDown className="w-4 h-4 opacity-50" />
            </div>
          </Button>
        </PopoverTrigger>
        <PopoverContent 
          className={cn(
            "p-0",
            variant === "form" && "w-full",
            contentClassName
          )}
          style={{ width: "var(--radix-popover-trigger-width)" }}
        >
          <CommandContent />
        </PopoverContent>
      </Popover>
    );
  }
);

Combobox.displayName = "Combobox";

export default Combobox;