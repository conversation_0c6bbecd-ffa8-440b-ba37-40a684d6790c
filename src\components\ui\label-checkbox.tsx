import * as React from "react"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { cn } from "@/lib/utils"

export interface LabelCheckboxProps
  extends React.ComponentPropsWithoutRef<typeof Checkbox> {
  label?: string
  labelClassName?: string
}

const LabelCheckbox = React.forwardRef<
  React.ComponentRef<typeof Checkbox>,
  LabelCheckboxProps
>(({ className, label, labelClassName, id, ...props }, ref) => {
  const generatedId = React.useId()
  const checkboxId = id || generatedId

  return (
    <div className="flex items-center space-x-2">
      <Checkbox
        ref={ref}
        id={checkboxId}
        className={className}
        {...props}
      />
      {label && (
        <Label
          htmlFor={checkboxId}
          className={cn(
            "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
            labelClassName
          )}
        >
          {label}
        </Label>
      )}
    </div>
  )
})

LabelCheckbox.displayName = "LabelCheckbox"

export { LabelCheckbox }
