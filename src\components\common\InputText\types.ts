import type { InputHTMLAttributes } from 'react';
import type { Control,RegisterOptions } from 'react-hook-form';

export type InputTextSize = 'sm' | 'md' | 'lg';

export type InputTextVariant = 
  | 'default'
  | 'with-label' 
  | 'with-button'
  | 'with-text'
  | 'with-icon'
  | 'file'
  | 'disabled'
  | 'form';

export interface BaseInputTextProps extends Omit<InputHTMLAttributes<HTMLInputElement>, 'size'> {
  variant?: InputTextVariant;
  size?: InputTextSize;
  className?: string;
  error?: boolean;
}

export interface WithLabelProps extends BaseInputTextProps {
  variant: 'with-label';
  label: string;
  htmlFor?: string;
}

export interface WithButtonProps extends BaseInputTextProps {
  variant: 'with-button';
  buttonText: string;
  onButtonClick: () => void;
  buttonDisabled?: boolean;
}

export interface WithTextProps extends BaseInputTextProps {
  variant: 'with-text';
  leadingText?: string;
  trailingText?: string;
}

export interface WithIconProps extends BaseInputTextProps {
  variant: 'with-icon';
  icon: React.ReactNode;
  iconPosition?: 'left' | 'right';
}

export interface FileInputProps extends BaseInputTextProps {
  variant: 'file';
  accept?: string;
  multiple?: boolean;
}

export interface DisabledInputProps extends BaseInputTextProps {
  variant: 'disabled';
  disabled: true;
}

export interface FormInputProps extends BaseInputTextProps {
  variant: 'form';
  name: string;
  control: Control<any>;
  label?: string;
  description?: string;
  rules?: RegisterOptions;
}

export type InputTextProps = 
  | BaseInputTextProps
  | WithLabelProps
  | WithButtonProps
  | WithTextProps
  | WithIconProps
  | FileInputProps
  | DisabledInputProps
  | FormInputProps;