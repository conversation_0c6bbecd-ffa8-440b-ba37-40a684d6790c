import type { Meta, StoryObj } from '@storybook/react-vite';
import { useForm } from 'react-hook-form';
import { Form } from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { InputOTP } from './InputOTP';
import React from 'react';

const meta = {
  title: 'Components/InputOTP',
  component: InputOTP,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A unified OTP input component with accessibility, copy/paste functionality, and multiple layout variants.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['default', 'with-separator', 'pattern', 'form', 'disabled'],
    },
    maxLength: {
      control: { type: 'number', min: 4, max: 8 },
    },
  },
} satisfies Meta<typeof InputOTP>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    maxLength: 6,
  },
};

export const AllVariants: Story = {
  render: () => {
    const [otp1, setOtp1] = React.useState('');
    const [otp2, setOtp2] = React.useState('');
    const [otp3, setOtp3] = React.useState('');

    return (
      <div className="grid gap-8 p-6 max-w-2xl">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Default OTP</h3>
          <p className="text-sm text-muted-foreground">
            Basic 6-digit OTP input with automatic focus navigation
          </p>
          <InputOTP
            maxLength={6}
            value={otp1}
            onChange={setOtp1}
          />
          {otp1 && (
            <p className="text-sm text-muted-foreground">Value: {otp1}</p>
          )}
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-semibold">With Separator</h3>
          <p className="text-sm text-muted-foreground">
            OTP input with visual separators between groups
          </p>
          <div className="space-y-3">
            <div>
              <label className="text-sm font-medium">3-3 Split</label>
              <InputOTP
                variant="with-separator"
                maxLength={6}
                groupSizes={[3, 3]}
                value={otp2}
                onChange={setOtp2}
              />
            </div>
            <div>
              <label className="text-sm font-medium">2-2-2 Split</label>
              <InputOTP
                variant="with-separator"
                maxLength={6}
                groupSizes={[2, 2, 2]}
                separatorChar="•"
              />
            </div>
            <div>
              <label className="text-sm font-medium">4-4 Split (8 digits)</label>
              <InputOTP
                variant="with-separator"
                maxLength={8}
                groupSizes={[4, 4]}
              />
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Pattern Validation</h3>
          <p className="text-sm text-muted-foreground">
            OTP inputs with different validation patterns
          </p>
          <div className="space-y-3">
            <div>
              <label className="text-sm font-medium">Digits Only</label>
              <InputOTP
                variant="pattern"
                maxLength={6}
                pattern={/^[0-9]*$/}
                patternDescription="Only numbers (0-9) are allowed"
                value={otp3}
                onChange={setOtp3}
              />
            </div>
            <div>
              <label className="text-sm font-medium">Alphanumeric</label>
              <InputOTP
                variant="pattern"
                maxLength={8}
                pattern={/^[A-Za-z0-9]*$/}
                patternDescription="Letters and numbers are allowed"
              />
            </div>
            <div>
              <label className="text-sm font-medium">Uppercase Only</label>
              <InputOTP
                variant="pattern"
                maxLength={6}
                pattern={/^[A-Z0-9]*$/}
                patternDescription="Uppercase letters and numbers only"
              />
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Disabled State</h3>
          <p className="text-sm text-muted-foreground">
            Non-interactive OTP display
          </p>
          <InputOTP
            variant="disabled"
            maxLength={6}
            value="123456"
          />
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Different Lengths</h3>
          <div className="space-y-3">
            <div>
              <label className="text-sm font-medium">4 Digits</label>
              <InputOTP maxLength={4} />
            </div>
            <div>
              <label className="text-sm font-medium">5 Digits</label>
              <InputOTP maxLength={5} />
            </div>
            <div>
              <label className="text-sm font-medium">8 Digits</label>
              <InputOTP maxLength={8} />
            </div>
          </div>
        </div>
      </div>
    );
  },
};

// Form Integration Example
const FormExample = () => {
  const form = useForm({
    defaultValues: {
      verificationCode: '',
      backupCode: '',
      twoFactorCode: '',
    },
  });

  const onSubmit = (data: any) => {
    console.log('Form submitted:', data);
    alert('Form submitted! Check console for data.');
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 max-w-md">
        <InputOTP
          variant="form"
          name="verificationCode"
          control={form.control}
          label="Verification Code"
          description="Enter the 6-digit code sent to your email"
          maxLength={6}
          rules={{ 
            required: 'Verification code is required',
            minLength: {
              value: 6,
              message: 'Code must be 6 digits'
            }
          }}
        />

        <InputOTP
          variant="form"
          name="backupCode"
          control={form.control}
          label="Backup Code"
          description="Enter your 8-character backup code"
          maxLength={8}
          rules={{ 
            required: 'Backup code is required',
            minLength: {
              value: 8,
              message: 'Backup code must be 8 characters'
            }
          }}
        />

        <InputOTP
          variant="form"
          name="twoFactorCode"
          control={form.control}
          label="Two-Factor Authentication"
          description="Enter the code from your authenticator app"
          maxLength={6}
          rules={{ 
            required: 'Two-factor code is required',
            pattern: {
              value: /^[0-9]{6}$/,
              message: 'Code must be 6 digits'
            }
          }}
        />

        <Button type="submit" className="w-full">Verify Codes</Button>
      </form>
    </Form>
  );
};

export const FormIntegration: Story = {
  render: () => <FormExample />,
  parameters: {
    docs: {
      description: {
        story: 'Example of using InputOTP with React Hook Form, including validation for different code types.',
      },
    },
  },
};

export const Patterns: Story = {
  render: () => {
    const [values, setValues] = React.useState({
      digits: '',
      alphanumeric: '',
      uppercase: '',
      custom: '',
    });

    const validatePattern = (value: string, pattern: RegExp) => {
      return pattern.test(value);
    };

    return (
      <div className="grid gap-6 p-6 max-w-lg">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Pattern Validation Examples</h3>
          
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">SMS Code (Digits Only)</label>
              <InputOTP
                variant="pattern"
                maxLength={6}
                pattern={/^[0-9]*$/}
                patternDescription="0-9 only"
                value={values.digits}
                onChange={(value: any) => setValues(prev => ({ ...prev, digits: value }))}
              />
              {values.digits && (
                <p className={`text-xs mt-1 ${validatePattern(values.digits, /^[0-9]{6}$/) ? 'text-green-600' : 'text-red-600'}`}>
                  {validatePattern(values.digits, /^[0-9]{6}$/) ? '✓ Valid' : '✗ Must be 6 digits'}
                </p>
              )}
            </div>

            <div>
              <label className="text-sm font-medium">Alphanumeric Token</label>
              <InputOTP
                variant="pattern"
                maxLength={8}
                pattern={/^[A-Za-z0-9]*$/}
                patternDescription="Letters and numbers"
                value={values.alphanumeric}
                onChange={(value: any) => setValues(prev => ({ ...prev, alphanumeric: value }))}
              />
              {values.alphanumeric && (
                <p className={`text-xs mt-1 ${validatePattern(values.alphanumeric, /^[A-Za-z0-9]{8}$/) ? 'text-green-600' : 'text-red-600'}`}>
                  {validatePattern(values.alphanumeric, /^[A-Za-z0-9]{8}$/) ? '✓ Valid' : '✗ Must be 8 alphanumeric characters'}
                </p>
              )}
            </div>

            <div>
              <label className="text-sm font-medium">Security Code (Uppercase)</label>
              <InputOTP
                variant="pattern"
                maxLength={6}
                pattern={/^[A-Z0-9]*$/}
                patternDescription="Uppercase letters and numbers"
                value={values.uppercase}
                onChange={(value: any) => setValues(prev => ({ ...prev, uppercase: value.toUpperCase() }))}
              />
              {values.uppercase && (
                <p className={`text-xs mt-1 ${validatePattern(values.uppercase, /^[A-Z0-9]{6}$/) ? 'text-green-600' : 'text-red-600'}`}>
                  {validatePattern(values.uppercase, /^[A-Z0-9]{6}$/) ? '✓ Valid' : '✗ Must be 6 uppercase characters/numbers'}
                </p>
              )}
            </div>

            <div>
              <label className="text-sm font-medium">Custom Pattern (Letters Only)</label>
              <InputOTP
                variant="pattern"
                maxLength={4}
                pattern={/^[A-Za-z]*$/}
                patternDescription="Letters only (A-Z, a-z)"
                value={values.custom}
                onChange={(value: any) => setValues(prev => ({ ...prev, custom: value }))}
              />
              {values.custom && (
                <p className={`text-xs mt-1 ${validatePattern(values.custom, /^[A-Za-z]{4}$/) ? 'text-green-600' : 'text-red-600'}`}>
                  {validatePattern(values.custom, /^[A-Za-z]{4}$/) ? '✓ Valid' : '✗ Must be 4 letters only'}
                </p>
              )}
            </div>
          </div>

          <div className="mt-6 p-4 bg-muted rounded-md">
            <h4 className="font-medium mb-2">Current Values:</h4>
            <div className="text-sm space-y-1">
              <p>Digits: {values.digits || 'Empty'}</p>
              <p>Alphanumeric: {values.alphanumeric || 'Empty'}</p>
              <p>Uppercase: {values.uppercase || 'Empty'}</p>
              <p>Custom: {values.custom || 'Empty'}</p>
            </div>
          </div>
        </div>
      </div>
    );
  },
};

export const Interactive: Story = {
  render: () => {
    const [otp, setOtp] = React.useState('');
    const [isComplete, setIsComplete] = React.useState(false);

    React.useEffect(() => {
      setIsComplete(otp.length === 6);
    }, [otp]);

    const handleClear = () => {
      setOtp('');
      setIsComplete(false);
    };

    const handleFill = () => {
      setOtp('123456');
    };

    const handleFillAlpha = () => {
      setOtp('ABC123');
    };

    return (
      <div className="grid gap-6 p-6 max-w-lg">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Interactive OTP Demo</h3>
          
          <InputOTP
            variant="with-separator"
            maxLength={6}
            groupSizes={[3, 3]}
            value={otp}
            onChange={setOtp}
          />

          <div className="flex gap-2 flex-wrap">
            <Button variant="outline" size="sm" onClick={handleClear}>
              Clear
            </Button>
            <Button variant="outline" size="sm" onClick={handleFill}>
              Fill Numbers
            </Button>
            <Button variant="outline" size="sm" onClick={handleFillAlpha}>
              Fill Mixed
            </Button>
          </div>

          <div className="space-y-2">
            <p className="text-sm">
              <strong>Value:</strong> {otp || 'Empty'}
            </p>
            <p className="text-sm">
              <strong>Length:</strong> {otp.length}/6
            </p>
            <p className={`text-sm ${isComplete ? 'text-green-600' : 'text-muted-foreground'}`}>
              <strong>Status:</strong> {isComplete ? '✓ Complete' : 'Incomplete'}
            </p>
          </div>

          <div className="p-4 bg-muted rounded-md">
            <h4 className="font-medium mb-2">Try These Features:</h4>
            <ul className="text-sm space-y-1">
              <li>• Type digits to auto-advance</li>
              <li>• Use arrow keys to navigate</li>
              <li>• Backspace to go back</li>
              <li>• Paste a 6-digit code</li>
              <li>• Select all with Ctrl+A</li>
              <li>• Delete key to clear slot</li>
            </ul>
          </div>
        </div>
      </div>
    );
  },
};

export const Separators: Story = {
  render: () => (
    <div className="grid gap-6 p-6 max-w-lg">
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Separator Variations</h3>
        <div className="space-y-4">
          <div>
            <label className="text-sm font-medium">Default Separator (−)</label>
            <InputOTP
              variant="with-separator"
              maxLength={6}
              groupSizes={[3, 3]}
            />
          </div>

          <div>
            <label className="text-sm font-medium">Dot Separator (•)</label>
            <InputOTP
              variant="with-separator"
              maxLength={6}
              groupSizes={[3, 3]}
              separatorChar="•"
            />
          </div>

          <div>
            <label className="text-sm font-medium">Dash Separator (—)</label>
            <InputOTP
              variant="with-separator"
              maxLength={6}
              groupSizes={[3, 3]}
              separatorChar="—"
            />
          </div>

          <div>
            <label className="text-sm font-medium">Space Separator</label>
            <InputOTP
              variant="with-separator"
              maxLength={6}
              groupSizes={[3, 3]}
              separatorChar=" "
            />
          </div>

          <div>
            <label className="text-sm font-medium">Three Groups (2-2-2)</label>
            <InputOTP
              variant="with-separator"
              maxLength={6}
              groupSizes={[2, 2, 2]}
              separatorChar="|"
            />
          </div>

          <div>
            <label className="text-sm font-medium">Four Groups (2-2-2-2)</label>
            <InputOTP
              variant="with-separator"
              maxLength={8}
              groupSizes={[2, 2, 2, 2]}
              separatorChar="·"
            />
          </div>
        </div>
      </div>
    </div>
  ),
};

export const Accessibility: Story = {
  render: () => (
    <div className="grid gap-6 p-6 max-w-lg">
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Accessibility Features</h3>
        <div className="space-y-4">
          <div>
            <label htmlFor="accessible-otp" className="text-sm font-medium">
              Verification Code
            </label>
            <p className="text-xs text-muted-foreground mb-2">
              Enter the 6-digit code sent to your email
            </p>
            <InputOTP
              maxLength={6}
              containerClassName="aria-describedby-otp-help"
            />
            <p id="otp-help" className="text-xs text-muted-foreground mt-1">
              Each digit will auto-advance to the next field
            </p>
          </div>

          <div>
            <label className="text-sm font-medium">
              Security Code with Groups
            </label>
            <InputOTP
              variant="with-separator"
              maxLength={6}
              groupSizes={[3, 3]}
            />
          </div>

          <div>
            <label className="text-sm font-medium">
              Disabled Example
            </label>
            <InputOTP
              variant="disabled"
              maxLength={6}
              value="123456"
            />
          </div>

          <div className="text-sm text-muted-foreground">
            <h4 className="font-medium mb-2">Keyboard Support:</h4>
            <ul className="space-y-1">
              <li>• Arrow Left/Right: Navigate between slots</li>
              <li>• Backspace: Delete and move to previous slot</li>
              <li>• Delete: Clear current slot</li>
              <li>• Home/End: Jump to first/last slot</li>
              <li>• Ctrl+A: Select all</li>
              <li>• Ctrl+V: Paste code</li>
            </ul>
          </div>

          <div className="text-sm text-muted-foreground">
            <h4 className="font-medium mb-2">Screen Reader Support:</h4>
            <ul className="space-y-1">
              <li>• Proper ARIA labels and descriptions</li>
              <li>• Character announcement on input</li>
              <li>• Position announcement when navigating</li>
              <li>• Error state announcements</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Demonstrates comprehensive accessibility features including ARIA support, keyboard navigation, and screen reader compatibility.',
      },
    },
  },
};