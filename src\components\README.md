# Components Directory

This directory contains all reusable components organized by their purpose and scope.

## Directory Structure

```
src/components/
├── common/           # Common components used across the application
│   └── Breadcrumb/
│       ├── index.ts              # Export barrel
│       ├── Breadcrumb.tsx        # Main component
│       ├── Breadcrumb.stories.tsx # Storybook stories
│       ├── Breadcrumb.test.tsx   # Unit tests
│       └── types.ts              # TypeScript definitions
├── ui/               # UI components (shadcn/ui style)
│   └── Button/
│       ├── index.ts              # Export barrel
│       ├── Button.tsx            # Main component
│       ├── Button.stories.tsx    # Storybook stories
│       ├── Button.test.tsx       # Unit tests
│       └── types.ts              # TypeScript definitions
└── README.md         # This file
```

## Component Categories

### Common Components (`/common`)
- **Purpose**: Business logic components specific to your application
- **Examples**: Breadcrumb, Navigation, SearchBar, ProductCard
- **Location**: `src/components/common/[ComponentName]/`

### UI Components (`/ui`)
- **Purpose**: Reusable UI primitives and design system components
- **Examples**: Button, Input, Modal, Card
- **Location**: `src/components/ui/[ComponentName]/`

## Component Structure

Each component follows this structure:

```
ComponentName/
├── index.ts              # Export barrel (exports component and types)
├── ComponentName.tsx     # Main component implementation
├── ComponentName.stories.tsx # Storybook stories
├── ComponentName.test.tsx   # Unit tests
└── types.ts              # TypeScript type definitions
```

### File Purposes

- **`index.ts`**: Barrel export for clean imports
- **`ComponentName.tsx`**: Main component with implementation
- **`ComponentName.stories.tsx`**: Storybook documentation and examples
- **`ComponentName.test.tsx`**: Unit tests using Vitest and React Testing Library
- **`types.ts`**: TypeScript interfaces and types

## Development Workflow

### 1. Creating a New Component

```bash
# Create component directory
mkdir -p src/components/[category]/[ComponentName]

# Create files
touch src/components/[category]/[ComponentName]/index.ts
touch src/components/[category]/[ComponentName]/[ComponentName].tsx
touch src/components/[category]/[ComponentName]/[ComponentName].stories.tsx
touch src/components/[category]/[ComponentName]/[ComponentName].test.tsx
touch src/components/[category]/[ComponentName]/types.ts
```

### 2. Importing Components

```tsx
// Clean barrel imports
import { Button } from '@/components/ui/Button';
import { Breadcrumb } from '@/components/common/Breadcrumb';

// Type imports
import type { ButtonProps } from '@/components/ui/Button';
import type { BreadcrumbProps } from '@/components/common/Breadcrumb';
```

### 3. Running Storybook

```bash
pnpm storybook
```

### 4. Running Tests

```bash
pnpm test
```

## Adding shadcn/ui Components

To add shadcn/ui components to the `/ui` directory:

```bash
npx shadcn-ui@latest add button
```

This will create the component in `src/components/ui/button/` following the shadcn/ui structure.

## Best Practices

1. **TypeScript First**: Always define types in `types.ts`
2. **Barrel Exports**: Use `index.ts` for clean imports
3. **Storybook**: Create comprehensive stories for all components
4. **Testing**: Write unit tests for all component logic
5. **Accessibility**: Ensure components meet a11y standards
6. **Responsive**: Test components across different viewport sizes

## Styling

- Use Tailwind CSS for styling
- Leverage CSS variables for theming
- Follow the design system defined in `tailwind.config.ts`
- Use the `cn()` utility for conditional classes

## Examples

See the existing `Breadcrumb` and `Button` components for reference implementations. 