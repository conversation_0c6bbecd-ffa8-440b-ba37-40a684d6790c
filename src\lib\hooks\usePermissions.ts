import { useAuthStore } from '../../stores/permissionStore'
import type { PermissionContext } from '../utils/permissions'

export const usePermissions = () => {
  const {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    hasRole,
    hasAnyRole,
    inGroup,
    inAnyGroup,
    featureEnabled,
    isFieldVisible,
    isFieldEditable,
    isFieldRequired,
  } = useAuthStore()

  return {
    can: (permission: string, context?: PermissionContext) => 
      hasPermission(permission, context),
    canAny: (permissions: string[], context?: PermissionContext) => 
      hasAnyPermission(permissions, context),
    canAll: (permissions: string[], context?: PermissionContext) => 
      hasAllPermissions(permissions, context),
    hasRole,
    hasAnyRole,
    inGroup,
    inAnyGroup,
    hasFeature: featureEnabled,
    canViewField: isFieldVisible,
    canEditField: isFieldEditable,
    isFieldRequired,
  }
}

export const useAuth = () => {
  const state = useAuthStore()
  return {
    ...state,
    isAuthenticated: !!state.organizationId && !state.loading,
    isLoading: state.loading,
  }
} 