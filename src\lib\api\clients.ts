import axios from 'axios'
import { useAuthStore } from '@/stores/authStore'
import { useUIStore } from '@/stores/uiStore'

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL

export const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// Request interceptor - Add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = useAuthStore.getState().token
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => Promise.reject(error)
)

// Response interceptor - Handle errors globally
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    const { addToast } = useUIStore.getState()
    const { logout } = useAuthStore.getState()
    
    const status = error.response?.status
    const errorData = error.response?.data
    
    switch (status) {
      case 401:
        // Authentication Error
        logout()
        addToast({
          type: 'error',
          title: 'Session Expired',
          message: 'Please log in again'
        })
        window.location.href = '/auth/signin'
        break
        
      case 403:
        // Authorization Error
        addToast({
          type: 'error',
          title: 'Access Denied',
          message: errorData?.message || 'You do not have permission to perform this action'
        })
        break
        
      case 404:
        // Not Found Error
        addToast({
          type: 'error',
          title: 'Not Found',
          message: errorData?.message || 'The requested resource was not found'
        })
        break
        
      case 409:
        // Conflict Error
        addToast({
          type: 'error',
          title: 'Conflict',
          message: errorData?.message || 'Resource already exists or cannot be updated'
        })
        break
        
      case 429:
        // Rate Limit Error
        addToast({
          type: 'warning',
          title: 'Too Many Requests',
          message: errorData?.message || 'Please slow down and try again',
          duration: 8000
        })
        break
        
      case 400:
        // Bad Request
        addToast({
          type: 'error',
          title: 'Bad Request',
          message: errorData?.message || 'Invalid request. Please check your input'
        })
        break
        
      case 405:
        // Method Not Allowed
        addToast({
          type: 'error',
          title: 'Method Not Allowed',
          message: 'This action is not supported'
        })
        break
        
      case 503:
        // Service Unavailable
        addToast({
          type: 'error',
          title: 'Service Unavailable',
          message: 'Service is temporarily unavailable. Please try again later',
          duration: 8000
        })
        break
        
      case 500:
      default:
        // Server Error or Unknown Error
        if (status >= 500) {
          addToast({
            type: 'error',
            title: 'Server Error',
            message: 'Something went wrong on our end. Please try again'
          })
        } else if (status !== 422) {
          // Don't show toast for validation errors - handle them in forms
          addToast({
            type: 'error',
            title: 'Error',
            message: errorData?.message || 'An unexpected error occurred'
          })
        }
        break
    }
    
    return Promise.reject(error)
  }
)
