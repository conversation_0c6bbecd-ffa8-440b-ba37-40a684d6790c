import * as React from "react"
import { ChevronLeft, ChevronRight, MoreHorizontal } from "lucide-react"

import { cn } from "@/lib/utils"
import { Button, buttonVariants } from "@/components/ui/button"

function Pagination({ className, ...props }: React.ComponentProps<"nav">) {
  return (
    <nav
      data-slot="pagination"
      role="navigation"
      aria-label="pagination"
      className={cn("mx-auto flex w-full justify-center", className)}
      {...props}
    />
  )
}

function PaginationContent({ className, ...props }: React.ComponentProps<"ul">) {
  return (
    <ul
      data-slot="pagination-content"
      className={cn("flex flex-row items-center gap-1", className)}
      {...props}
    />
  )
}

function PaginationItem({ className, ...props }: React.ComponentProps<"li">) {
  return (
    <li
      data-slot="pagination-item"
      className={cn("", className)}
      {...props}
    />
  )
}

type PaginationLinkProps = {
  isActive?: boolean
} & (
  | React.ComponentProps<"a">
  | (React.ComponentProps<typeof Button> & { href?: never })
)

function PaginationLink({
  className,
  isActive,
  ...props
}: PaginationLinkProps) {
  const sharedClasses = cn(
    buttonVariants({
      variant: isActive ? "outline" : "ghost",
      size: "icon",
    }),
    className
  )

  if ("href" in props && props.href) {
    return (
      <a
        data-slot="pagination-link"
        aria-current={isActive ? "page" : undefined}
        className={sharedClasses}
        {...(props as React.ComponentProps<"a">)}
      />
    )
  }

  return (
    <Button
      data-slot="pagination-link"
      aria-current={isActive ? "page" : undefined}
      variant={isActive ? "outline" : "ghost"}
      size="icon"
      className={className}
      {...(props as React.ComponentProps<typeof Button>)}
    />
  )
}

type PaginationPreviousProps = (
  | React.ComponentProps<"a">
  | (React.ComponentProps<typeof Button> & { href?: never })
)

function PaginationPrevious({
  className,
  children = "Previous",
  ...props
}: PaginationPreviousProps & { children?: React.ReactNode }) {
  const sharedClasses = cn(
    buttonVariants({
      variant: "ghost",
      size: "default",
    }),
    "gap-1 pl-2.5",
    className
  )

  const content = (
    <>
      <ChevronLeft className="h-4 w-4" />
      <span>{children}</span>
    </>
  )

  if ("href" in props && props.href) {
    return (
      <a
        data-slot="pagination-previous"
        aria-label="Go to previous page"
        className={sharedClasses}
        {...(props as React.ComponentProps<"a">)}
      >
        {content}
      </a>
    )
  }

  return (
    <Button
      data-slot="pagination-previous"
      aria-label="Go to previous page"
      variant="ghost"
      size="default"
      className={cn("gap-1 pl-2.5", className)}
      {...(props as React.ComponentProps<typeof Button>)}
    >
      {content}
    </Button>
  )
}

type PaginationNextProps = (
  | React.ComponentProps<"a">
  | (React.ComponentProps<typeof Button> & { href?: never })
)

function PaginationNext({
  className,
  children = "Next",
  ...props
}: PaginationNextProps & { children?: React.ReactNode }) {
  const sharedClasses = cn(
    buttonVariants({
      variant: "ghost",
      size: "default",
    }),
    "gap-1 pr-2.5",
    className
  )

  const content = (
    <>
      <span>{children}</span>
      <ChevronRight className="h-4 w-4" />
    </>
  )

  if ("href" in props && props.href) {
    return (
      <a
        data-slot="pagination-next"
        aria-label="Go to next page"
        className={sharedClasses}
        {...(props as React.ComponentProps<"a">)}
      >
        {content}
      </a>
    )
  }

  return (
    <Button
      data-slot="pagination-next"
      aria-label="Go to next page"
      variant="ghost"
      size="default"
      className={cn("gap-1 pr-2.5", className)}
      {...(props as React.ComponentProps<typeof Button>)}
    >
      {content}
    </Button>
  )
}

function PaginationEllipsis({
  className,
  ...props
}: React.ComponentProps<"span">) {
  return (
    <span
      data-slot="pagination-ellipsis"
      aria-hidden
      className={cn("flex h-9 w-9 items-center justify-center", className)}
      {...props}
    >
      <MoreHorizontal className="h-4 w-4" />
      <span className="sr-only">More pages</span>
    </span>
  )
}

export {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
}
