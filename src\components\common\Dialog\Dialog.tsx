import React, { useState } from 'react';
import { Co<PERSON>, Check } from 'lucide-react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface DialogField {
  id: string;
  label: string;
  placeholder: string;
  value?: string;
}

interface DialogProps {
  variant?: 'default' | 'mobile-first' | 'desktop-first';
  title?: string;
  description?: string;
  fields?: DialogField[];
  primaryAction?: {
    label: string;
    onClick: () => void;
  };
  secondaryAction?: {
    label: string;
    onClick: () => void;
  };
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  copyable?: boolean;
  className?: string;
}

const CustomDialog: React.FC<DialogProps> = ({
  variant = 'default',
  title = 'Share link',
  description = 'Anyone who has this link will be able to view this.',
  fields = [
    { id: 'field1', label: 'Field 1', placeholder: 'Placeholder', value: '' },
    { id: 'field2', label: 'Field 2', placeholder: 'Placeholder', value: '' }
  ],
  primaryAction = { label: 'CTA', onClick: () => {} },
  secondaryAction = { label: 'Close', onClick: () => {} },
  open = true,
  onOpenChange = () => {},
  copyable = false,
  className = ""
}) => {
  const [fieldValues, setFieldValues] = useState(
    fields.reduce((acc, field) => ({ ...acc, [field.id]: field.value || '' }), {} as Record<string, string>)
  );
  const [copied, setCopied] = useState(false);

  const handleFieldChange = (fieldId: string, value: string) => {
    setFieldValues(prev => ({ ...prev, [fieldId]: value }));
  };

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(Object.values(fieldValues).join('\n'));
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  const renderButtons = () => {
    switch (variant) {
      case 'mobile-first':
        // Mobile: Full-width stacked buttons, Desktop: Side-by-side
        return (
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-2 sm:justify-end">
            <Button 
              variant="outline" 
              onClick={secondaryAction.onClick}
              className="w-full sm:w-auto"
            >
              {secondaryAction.label}
            </Button>
            <Button 
              onClick={primaryAction.onClick}
              className="w-full sm:w-auto"
            >
              {primaryAction.label}
            </Button>
          </div>
        );

      case 'desktop-first':
        // Desktop: Side-by-side buttons, Mobile: Full-width stacked (reverse order)
        return (
          <div className="flex flex-col-reverse sm:flex-row gap-3 sm:gap-2 sm:justify-end">
            <Button 
              variant="outline" 
              onClick={secondaryAction.onClick}
              className="w-full sm:w-auto"
            >
              {secondaryAction.label}
            </Button>
            <Button 
              onClick={primaryAction.onClick}
              className="w-full sm:w-auto"
            >
              {primaryAction.label}
            </Button>
          </div>
        );

      default:
        // Standard shadcn/ui dialog footer pattern
        return (
          <div className="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2">
            <Button 
              variant="outline" 
              onClick={secondaryAction.onClick}
            >
              {secondaryAction.label}
            </Button>
            <Button onClick={primaryAction.onClick}>
              {primaryAction.label}
            </Button>
          </div>
        );
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className={className}>
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          {fields.map((field) => (
            <div key={field.id} className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor={field.id} className="text-right">
                {field.label}
              </Label>
              <Input
                id={field.id}
                placeholder={field.placeholder}
                value={fieldValues[field.id]}
                onChange={(e) => handleFieldChange(field.id, e.target.value)}
                className="col-span-3"
              />
            </div>
          ))}
        </div>

        {copyable && (
          <Button
            variant="outline"
            size="sm"
            onClick={handleCopy}
            className="w-fit"
          >
            {copied ? (
              <>
                <Check className="mr-2 h-4 w-4" />
                Copied!
              </>
            ) : (
              <>
                <Copy className="mr-2 h-4 w-4" />
                Copy
              </>
            )}
          </Button>
        )}

        <DialogFooter>
          {renderButtons()}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};


export { CustomDialog };