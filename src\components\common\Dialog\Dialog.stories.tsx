import type { <PERSON>a, StoryObj } from '@storybook/react-vite';
import { useState } from 'react';
import { CustomDialog } from './Dialog';
import { Button } from '@/components/ui/button';

const meta: Meta<typeof CustomDialog> = {
  title: 'Common/Dialog',
  component: CustomDialog,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A unified Dialog component that consolidates all shadcn/ui dialog patterns with responsive button layouts for mobile and desktop views.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['default', 'mobile-first', 'desktop-first'],
      description: 'Different button layout patterns for mobile and desktop',
    },
    title: {
      control: 'text',
      description: 'Dialog title',
    },
    description: {
      control: 'text',
      description: 'Dialog description text',
    },
    copyable: {
      control: 'boolean',
      description: 'Shows copy button for field values',
    },
    open: {
      control: 'boolean',
      description: 'Controls dialog visibility',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Wrapper component for stories that need state management
type DialogWrapperProps = {
  variant?: 'default' | 'mobile-first' | 'desktop-first';
  [x: string]: any;
};
const DialogWrapper = ({ variant, ...props }: DialogWrapperProps) => {
  const [open, setOpen] = useState(false);

  return (
    <div>
      <Button onClick={() => setOpen(true)}>
        Open {variant || 'Default'} Dialog
      </Button>
      <CustomDialog
        {...props}
        variant={variant}
        open={open}
        onOpenChange={setOpen}
        primaryAction={{ 
          label: 'Save Changes', 
          onClick: () => {
            console.log('Primary action clicked');
            setOpen(false);
          }
        }}
        secondaryAction={{ 
          label: 'Cancel', 
          onClick: () => setOpen(false) 
        }}
      />
    </div>
  );
};

// Default story - shows the default dialog pattern
export const Default: Story = {
  render: (args) => <DialogWrapper {...args} />,
  args: {
    variant: 'default',
    title: 'Edit Profile',
    description: 'Make changes to your profile here. Click save when you\'re done.',
    fields: [
      { id: 'name', label: 'Name', placeholder: 'Enter your name' },
      { id: 'email', label: 'Email', placeholder: 'Enter your email' }
    ],
    copyable: false,
  },
};

// All Variants story - shows ALL dialog patterns in one view
export const AllVariants: Story = {
  render: () => {
    const [openStates, setOpenStates] = useState({
      default: false,
      mobileFirst: false,
      desktopFirst: false,
    });

    const toggleDialog = (variant: keyof typeof openStates) => {
      setOpenStates(prev => ({ ...prev, [variant]: !prev[variant] }));
    };

    const baseProps = {
      title: 'Share Link',
      description: 'Anyone who has this link will be able to view this.',
      fields: [
        { id: 'field1', label: 'Link URL', placeholder: 'https://example.com/share' },
        { id: 'field2', label: 'Access Code', placeholder: 'Enter access code' }
      ],
    };

    return (
      <div className="grid gap-6 p-4">
        <div className="text-center">
          <h3 className="text-lg font-semibold mb-2">All Dialog Variants</h3>
          <p className="text-sm text-gray-600 mb-6">
            Compare button layouts across mobile and desktop breakpoints
          </p>
        </div>

        <div className="grid gap-4 max-w-2xl mx-auto">
          {/* Default Variant */}
          <div className="border rounded-lg p-4">
            <div className="flex justify-between items-center mb-2">
              <div>
                <h4 className="font-medium">Default</h4>
                <p className="text-sm text-gray-600">Standard shadcn/ui pattern</p>
              </div>
              <Button onClick={() => toggleDialog('default')}>
                Open Default
              </Button>
            </div>
          </div>

          {/* Mobile-First Variant */}
          <div className="border rounded-lg p-4">
            <div className="flex justify-between items-center mb-2">
              <div>
                <h4 className="font-medium">Mobile-First</h4>
                <p className="text-sm text-gray-600">Full-width mobile, side-by-side desktop</p>
              </div>
              <Button onClick={() => toggleDialog('mobileFirst')}>
                Open Mobile-First
              </Button>
            </div>
          </div>

          {/* Desktop-First Variant */}
          <div className="border rounded-lg p-4">
            <div className="flex justify-between items-center mb-2">
              <div>
                <h4 className="font-medium">Desktop-First</h4>
                <p className="text-sm text-gray-600">Side-by-side desktop, full-width mobile</p>
              </div>
              <Button onClick={() => toggleDialog('desktopFirst')}>
                Open Desktop-First
              </Button>
            </div>
          </div>
        </div>

        {/* Dialog instances */}
        <CustomDialog
          {...baseProps}
          variant="default"
          open={openStates.default}
          onOpenChange={(open) => setOpenStates(prev => ({ ...prev, default: open }))}
          primaryAction={{ 
            label: 'Share', 
            onClick: () => setOpenStates(prev => ({ ...prev, default: false }))
          }}
          secondaryAction={{ 
            label: 'Cancel', 
            onClick: () => setOpenStates(prev => ({ ...prev, default: false }))
          }}
          copyable
        />

        <CustomDialog
          {...baseProps}
          variant="mobile-first"
          open={openStates.mobileFirst}
          onOpenChange={(open) => setOpenStates(prev => ({ ...prev, mobileFirst: open }))}
          primaryAction={{ 
            label: 'Share', 
            onClick: () => setOpenStates(prev => ({ ...prev, mobileFirst: false }))
          }}
          secondaryAction={{ 
            label: 'Cancel', 
            onClick: () => setOpenStates(prev => ({ ...prev, mobileFirst: false }))
          }}
        />

        <CustomDialog
          {...baseProps}
          variant="desktop-first"
          open={openStates.desktopFirst}
          onOpenChange={(open) => setOpenStates(prev => ({ ...prev, desktopFirst: open }))}
          primaryAction={{ 
            label: 'Share', 
            onClick: () => setOpenStates(prev => ({ ...prev, desktopFirst: false }))
          }}
          secondaryAction={{ 
            label: 'Cancel', 
            onClick: () => setOpenStates(prev => ({ ...prev, desktopFirst: false }))
          }}
        />
      </div>
    );
  },
};

// Interactive story - demonstrates user interactions
export const Interactive: Story = {
  render: () => {
    const [open, setOpen] = useState(false);
    const [variant, setVariant] = useState<'default' | 'mobile-first' | 'desktop-first'>('default');
    const [copyable, setCopyable] = useState(true);

    return (
      <div className="space-y-4 p-4">
        <div className="border rounded-lg p-4 space-y-4">
          <h3 className="font-semibold">Interactive Controls</h3>
          
          <div className="flex gap-4 flex-wrap">
            <div>
              <label className="text-sm font-medium">Variant:</label>
              <select 
                value={variant} 
                onChange={(e) => setVariant(e.target.value as any)}
                className="ml-2 border rounded px-2 py-1"
              >
                <option value="default">Default</option>
                <option value="mobile-first">Mobile-First</option>
                <option value="desktop-first">Desktop-First</option>
              </select>
            </div>
            
            <div className="flex items-center gap-2">
              <input 
                type="checkbox" 
                id="copyable" 
                checked={copyable}
                onChange={(e) => setCopyable(e.target.checked)}
              />
              <label htmlFor="copyable" className="text-sm font-medium">
                Show copy button
              </label>
            </div>
          </div>

          <Button onClick={() => setOpen(true)}>
            Open Interactive Dialog
          </Button>
        </div>

        <CustomDialog
          variant={variant}
          open={open}
          onOpenChange={setOpen}
          title="Interactive Dialog"
          description={`This is a ${variant} dialog variant. Resize your browser to see responsive behavior.`}
          fields={[
            { id: 'url', label: 'URL', placeholder: 'https://example.com' },
            { id: 'description', label: 'Description', placeholder: 'Optional description' }
          ]}
          primaryAction={{ 
            label: 'Continue', 
            onClick: () => {
              console.log('Continue clicked');
              setOpen(false);
            }
          }}
          secondaryAction={{ 
            label: 'Cancel', 
            onClick: () => setOpen(false) 
          }}
          copyable={copyable}
        />
      </div>
    );
  },
};

// Accessibility story - demonstrates keyboard navigation and ARIA support
export const Accessibility: Story = {
  render: () => {
    const [open, setOpen] = useState(false);

    return (
      <div className="space-y-4 p-4">
        <div className="border rounded-lg p-4 space-y-2">
          <h3 className="font-semibold">Accessibility Features</h3>
          <ul className="text-sm space-y-1 text-gray-600">
            <li>• <kbd>Tab</kbd> - Navigate between form fields and buttons</li>
            <li>• <kbd>Escape</kbd> - Close dialog</li>
            <li>• <kbd>Enter</kbd> - Activate primary button</li>
            <li>• Screen reader announcements for dialog state</li>
            <li>• Focus management (returns to trigger on close)</li>
            <li>• Proper ARIA labels and descriptions</li>
          </ul>
        </div>

        <Button onClick={() => setOpen(true)}>
          Open Accessible Dialog (Test with keyboard)
        </Button>

        <CustomDialog
          variant="mobile-first"
          open={open}
          onOpenChange={setOpen}
          title="Accessibility Test Dialog"
          description="Use Tab, Shift+Tab, Enter, and Escape to navigate this dialog."
          fields={[
            { id: 'name', label: 'Full Name', placeholder: 'Enter your full name' },
            { id: 'email', label: 'Email Address', placeholder: 'Enter your email' },
            { id: 'phone', label: 'Phone Number', placeholder: 'Enter your phone number' }
          ]}
          primaryAction={{ 
            label: 'Submit', 
            onClick: () => {
              alert('Form submitted! (Check console for details)');
              console.log('Accessibility test - form submitted');
              setOpen(false);
            }
          }}
          secondaryAction={{ 
            label: 'Cancel', 
            onClick: () => setOpen(false) 
          }}
        />
      </div>
    );
  },
};

// Form Integration story
export const FormIntegration: Story = {
  render: (args) => <DialogWrapper {...args} />,
  args: {
    variant: 'mobile-first',
    title: 'Add Team Member',
    description: 'Enter the details for the new team member.',
    fields: [
      { id: 'name', label: 'Full Name', placeholder: 'John Doe' },
      { id: 'email', label: 'Email', placeholder: '<EMAIL>' },
      { id: 'role', label: 'Role', placeholder: 'Software Engineer' }
    ],
    copyable: false,
  },
};

// Share Dialog story - matches your original screenshot
export const ShareDialog: Story = {
  render: (args) => <DialogWrapper {...args} />,
  args: {
    variant: 'mobile-first',
    title: 'Share link',
    description: 'Anyone who has this link will be able to view this.',
    fields: [
      { id: 'field1', label: 'Field 1', placeholder: 'Placeholder' },
      { id: 'field2', label: 'Field 2', placeholder: 'Placeholder' }
    ],
    copyable: true,
  },
};