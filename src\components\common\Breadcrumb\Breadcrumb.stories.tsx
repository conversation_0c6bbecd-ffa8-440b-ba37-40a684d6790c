import type { <PERSON><PERSON>, StoryObj } from '@storybook/react-vite';
import { Home, Folder, FileText, Settings } from 'lucide-react';
import { Breadcrumb } from './Breadcrumb';
import type { BreadcrumbItemData } from './types';

const meta: Meta<typeof Breadcrumb> = {
  title: 'Common/Breadcrumb',
  component: Breadcrumb,
  parameters: {
    docs: {
      description: {
        component: 'A navigation component that shows the current page location within a navigational hierarchy. Built on shadcn/ui breadcrumb primitives with additional variants and features.',
      },
    },
  },
  argTypes: {
    variant: {
      control: 'select',
      options: ['default', 'compact', 'with-icons', 'separated'],
      description: 'Visual variant of the breadcrumb'
    },
    size: {
      control: 'select',
      options: ['sm', 'md', 'lg'],
      description: 'Size variant of the breadcrumb'
    },
    maxItems: {
      control: 'number',
      description: 'Maximum number of items before truncation'
    },
    showHome: {
      control: 'boolean',
      description: 'Show home icon on first item'
    },
    onItemClick: {
      action: 'item-clicked',
      description: 'Callback when item is clicked'
    }
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

const defaultItems: BreadcrumbItemData[] = [
  { label: 'Home', href: '/' },
  { label: 'Products', href: '/products' },
  { label: 'Electronics', href: '/products/electronics' },
  { label: 'Smartphones', isCurrentPage: true }
];

const iconItems: BreadcrumbItemData[] = [
  { label: 'Dashboard', href: '/', icon: <Home className="h-4 w-4" /> },
  { label: 'Projects', href: '/projects', icon: <Folder className="h-4 w-4" /> },
  { label: 'Documentation', href: '/docs', icon: <FileText className="h-4 w-4" /> },
  { label: 'Settings', isCurrentPage: true, icon: <Settings className="h-4 w-4" /> }
];

const longItems: BreadcrumbItemData[] = [
  { label: 'Home', href: '/' },
  { label: 'Category', href: '/category' },
  { label: 'Subcategory', href: '/category/sub' },
  { label: 'Products', href: '/category/sub/products' },
  { label: 'Item Type', href: '/category/sub/products/type' },
  { label: 'Specific Item', href: '/category/sub/products/type/item' },
  { label: 'Current Page', isCurrentPage: true }
];

export const Default: Story = {
  args: {
    items: defaultItems,
    variant: 'default',
    size: 'md'
  },
};

export const AllVariants: Story = {
  render: () => (
    <div className="space-y-6">
      <div className="space-y-2">
        <h3 className="text-sm font-medium">Large</h3>
        <Breadcrumb items={defaultItems} size="lg" />
      </div>
    </div>
  ),
};

export const WithHomeIcon: Story = {
  args: {
    items: defaultItems,
    showHome: true
  },
};

export const Truncated: Story = {
  args: {
    items: longItems,
    maxItems: 4
  },
};

export const CustomSeparator: Story = {
  args: {
    items: defaultItems,
    separator: <span className="text-muted-foreground mx-1">/</span>
  },
};

export const Interactive: Story = {
  args: {
    items: defaultItems,
    onItemClick: (item, index) => {
      alert(`Clicked: ${item.label} at index ${index}`);
    }
  },
};

export const WithIcons: Story = {
  args: {
    items: iconItems,
    variant: 'with-icons'
  },
};

export const LongPath: Story = {
  args: {
    items: longItems
  },
};

export const AccessibilityDemo: Story = {
  render: () => (
    <div className="space-y-4">
      <p className="text-sm text-muted-foreground">
        Use Tab to navigate through clickable items. Current page items are marked with aria-current="page".
      </p>
      <Breadcrumb
        items={defaultItems}
        onItemClick={(item) => console.log('Navigate to:', item.href)}
      />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Demonstrates keyboard navigation and screen reader support. The component properly uses ARIA attributes and semantic HTML.',
      },
    },
  },
};

export const CompositionExample: Story = {
  render: () => (
    <div className="space-y-6 p-4 border rounded-lg">
      <div className="flex items-center justify-between">
        <Breadcrumb
          items={defaultItems}
          variant="with-icons"
          showHome={true}
        />
        <button className="px-3 py-1 text-sm border rounded hover:bg-accent">
          Share
        </button>
      </div>
      <div className="text-sm text-muted-foreground">
        Example showing breadcrumb in a page header layout
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Shows how the breadcrumb component integrates with other UI elements in a typical page header.',
      },
    },
  },
};