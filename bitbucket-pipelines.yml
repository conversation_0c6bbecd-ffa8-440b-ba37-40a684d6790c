image: node:20

pipelines:
  branches:
    dev:
      - step:
          name: Build & Deploy (Frontend Dev)
          size: 2x
          caches:
            - node
          script:
            - export NODE_OPTIONS="--max_old_space_size=4096"
            - echo "Fetching Dev .env..."
            - curl -o .env "$ENV_BLOB_DEV"
            - export $(grep -v '^#' .env | xargs)
            - echo "Installing dependencies..."
            - npm cache clean --force
            - rm -rf node_modules
            - npm install --legacy-peer-deps
            - echo "Running TypeScript type check..."
            - npx tsc --noEmit
            - echo "Building with Vite..."
            - npx vite build
            - ls -la dist/
            - find dist/ -type f -name "*.html" | xargs cat || true
            - npm install -g @azure/static-web-apps-cli
            - swa deploy dist --app-name migranium-frontend-dev --deployment-token $AZURE_SWA_TOKEN_DEV --env production --verbose

    staging:
      - step:
          name: Build & Deploy (Frontend Staging)
          size: 2x
          caches:
            - node
          script:
            - export NODE_OPTIONS="--max_old_space_size=4096"
            - curl -o .env "$ENV_BLOB_STAGING"
            - export $(grep -v '^#' .env | xargs)
            - npm cache clean --force
            - rm -rf node_modules
            - npm install --legacy-peer-deps
            - echo "Running TypeScript type check..."
            - npx tsc --noEmit
            - npx vite build
            - ls -la dist/
            - find dist/ -type f -name "*.html" | xargs cat || true
            - npm install -g @azure/static-web-apps-cli
            - swa deploy dist --app-name migranium-frontend-staging --deployment-token $AZURE_SWA_TOKEN_STAGING --env production --verbose

    main:
      - step:
          name: Build & Deploy (Frontend Prod)
          size: 2x
          caches:
            - node
          script:
            - export NODE_OPTIONS="--max_old_space_size=4096"
            - curl -o .env "$ENV_BLOB_PROD"
            - export $(grep -v '^#' .env | xargs)
            - npm cache clean --force
            - rm -rf node_modules
            - npm install --legacy-peer-deps
            - echo "Running TypeScript type check..."
            - npx tsc --noEmit
            - npx vite build
            - ls -la dist/
            - find dist/ -type f -name "*.html" | xargs cat || true
            - npm install -g @azure/static-web-apps-cli
            - swa deploy dist --app-name migranium-frontend-prod --deployment-token $AZURE_SWA_TOKEN_PROD --env production --verbose

  pull-requests:
    "**":
      - step:
          name: PR Build (No Deploy)
          size: 2x
          caches:
            - node
          script:
            - export NODE_OPTIONS="--max_old_space_size=4096"
            - curl -o .env "$ENV_BLOB_DEV"
            - export $(grep -v '^#' .env | xargs)
            - npm cache clean --force
            - rm -rf node_modules
            - npm install --legacy-peer-deps
            - echo "Running TypeScript type check..."
            - npx tsc --noEmit
            - npx vite build
            - ls -la dist/
            - find dist/ -type f -name "*.html" | xargs cat || true