import * as React from "react"
import { cn } from "@/lib/utils"

interface ProgressProps extends React.HTMLAttributes<HTMLDivElement> {
  value?: number
  max?: number
  getValueLabel?: (value: number, max: number) => string
}

function Progress({
  className,
  value,
  max = 100,
  getValueLabel,
  ...props
}: ProgressProps) {
  const percentage = value != null ? Math.min(Math.max((value / max) * 100, 0), 100) : null

  return (
    <div
      data-slot="progress"
      role="progressbar"
      aria-valuenow={value}
      aria-valuemin={0}
      aria-valuemax={max}
      aria-valuetext={getValueLabel?.(value ?? 0, max)}
      className={cn(
        "relative h-4 w-full overflow-hidden rounded-full bg-secondary",
        className
      )}
      {...props}
    >
      <div
        data-slot="progress-indicator"
        className={cn(
          "h-full w-full flex-1 bg-primary transition-all",
          percentage === null && "animate-pulse"
        )}
        style={{
          transform: `translateX(-${100 - (percentage ?? 0)}%)`,
        }}
      />
    </div>
  )
}

export { Progress }