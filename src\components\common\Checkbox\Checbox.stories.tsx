import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react-vite';
import { Checkbox } from './Checkbox';
import * as React from 'react';

const meta: Meta<typeof Checkbox> = {
  title: 'Common/Checkbox',
  component: Checkbox,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['default', 'with-text'],
    },
    disabled: {
      control: 'boolean',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    label: 'Accept terms and conditions',
  },
};

export const AllVariants: Story = {
  render: () => (
    <div className="grid gap-8 max-w-2xl">
      {/* Default Variant - exactly like shadcn/ui */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Default Variant</h3>
        <div className="grid gap-4">
          <Checkbox />
          <Checkbox label="Accept terms and conditions" />
          <Checkbox label="Checked by default" defaultChecked />
          <Checkbox label="Disabled" disabled />
        </div>
      </div>

      {/* With Text Variant - with description like shadcn/ui examples */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">With Text Variant</h3>
        <div className="grid gap-4">
          <Checkbox 
            variant="with-text"
            label="Accept terms and conditions"
            description="You agree to our Terms of Service and Privacy Policy."
          />
          <Checkbox 
            variant="with-text"
            label="Use different settings for my mobile devices"
            description="You can manage your mobile notifications in the mobile settings page."
            defaultChecked
          />
          <Checkbox 
            variant="with-text"
            label="Enable notifications"
            description="Get notified when someone posts a comment on a posting."
            disabled
          />
        </div>
      </div>
    </div>
  ),
};

export const Interactive: Story = {
  render: () => {
    const [items, setItems] = React.useState([
      { id: 'recents', label: 'Recents', checked: false },
      { id: 'home', label: 'Home', checked: true },
      { id: 'applications', label: 'Applications', checked: false },
      { id: 'desktop', label: 'Desktop', checked: true },
    ]);

    const handleItemChange = (id: string, checked: boolean) => {
      setItems(prev => prev.map(item => 
        item.id === id ? { ...item, checked } : item
      ));
    };

    return (
      <div className="space-y-6 max-w-md">
        <div>
          <h3 className="text-lg font-semibold mb-4">Select the items you want to display in the sidebar.</h3>
          <div className="space-y-3">
            {items.map(item => (
              <Checkbox
                key={item.id}
                label={item.label}
                checked={item.checked}
                onCheckedChange={(checked: any) => handleItemChange(item.id, checked)}
              />
            ))}
          </div>
        </div>
        <div className="mt-4 p-3 bg-muted rounded">
          <p className="text-sm font-medium">Selected items:</p>
          <p className="text-sm text-muted-foreground">
            {items.filter(item => item.checked).map(item => item.label).join(', ') || 'None'}
          </p>
        </div>
      </div>
    );
  },
};

export const FormExample: Story = {
  render: () => {
    const [termsAccepted, setTermsAccepted] = React.useState(false);
    const [notifications, setNotifications] = React.useState(true);

    return (
      <div className="space-y-6 max-w-md">
        <h3 className="text-lg font-semibold">Account Setup</h3>
        
        <Checkbox
          variant="with-text"
          label="I accept the terms and conditions"
          description="By checking this, you agree to our Terms of Service and Privacy Policy."
          checked={termsAccepted}
          onCheckedChange={setTermsAccepted}
        />

        <Checkbox
          variant="with-text"
          label="Send me notifications"
          description="You can manage your mobile notifications in the mobile settings page."
          checked={notifications}
          onCheckedChange={setNotifications}
        />

        <button 
          className="px-4 py-2 bg-primary text-primary-foreground rounded-md disabled:opacity-50"
          disabled={!termsAccepted}
        >
          Continue
        </button>
      </div>
    );
  },
};

export const Accessibility: Story = {
  render: () => (
    <div className="space-y-6 max-w-md">
      <h3 className="text-lg font-semibold">Accessibility Demo</h3>
      <div className="space-y-4">
        <Checkbox
          label="Keyboard accessible"
          description="Use Tab to focus and Space to toggle"
          defaultChecked
        />
        <Checkbox
          label="Screen reader friendly"
          description="Proper labels and ARIA attributes"
        />
        <Checkbox
          label="High contrast mode"
          disabled
        />
      </div>
      <p className="text-sm text-muted-foreground">
        Try using Tab and Space keys to navigate and interact
      </p>
    </div>
  ),
};