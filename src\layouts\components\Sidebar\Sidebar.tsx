import { cn } from "@/lib/utils";
import { NavLink } from "react-router";
import type { SidebarProps, NavigationItem } from "./types";
import { useFilteredNavigationItems } from "./utils";
import { useUIStore } from "@/stores/uiStore";
import { Button } from "@/components/ui/button";
import { Zap, ChevronRight, Settings, Clock } from "lucide-react";
import AnalyticsIcon from "@/assets/icons/sidebar/analytics";
import ClientsIcon from "@/assets/icons/sidebar/clients";
import DashboardIcon from "@/assets/icons/sidebar/dashboard";
import FormsIcon from "@/assets/icons/sidebar/forms";
import LogoutIcon from "@/assets/icons/sidebar/logout";
import ScheduleIcon from "@/assets/icons/sidebar/schedule";
// import SettingsIcon from "@/assets/icons/sidebar/settings";
// import TeamMembersIcon from "@/assets/icons/sidebar/teamMembers";
import HelpIcon from "@/assets/icons/sidebar/helpIcon";
import WaitlistIcon from "@/assets/icons/sidebar/waitlist";
import WorkplaceIcon from "@/assets/icons/sidebar/workplaceIcon";

const navigationItems: NavigationItem[] = [
	{
		id: "dashboard",
		label: "Dashboard",
		href: "/",
		icon: <DashboardIcon color="inherit" />,
		// Dashboard is accessible to all authenticated users
	},
	{
		id: "analytics",
		label: "Analytics",
		href: "/analytics",
		icon: <AnalyticsIcon />,
		// permissions: ['analytics:view'],
		// roles: ['admin', 'manager', 'analyst'],
	},
	{
		id: "waitlist",
		label: "Waitlist",
		href: "/waitlist",
		icon: <WaitlistIcon color="inherit" />,
		// permissions: ['waitlist:view'],
		// groups: ['waitlist_managers'],
	},
	{
		id: "schedule",
		label: "Schedule",
		href: "/schedule",
		icon: <ScheduleIcon />,
		children: [
			{
				id: "schedule-manage",
				label: "Manage",
				href: "/schedule/manage",
				icon: <Settings className="h-3.5 w-3.5" />,
			},
			{
				id: "schedule-history",
				label: "History",
				href: "/schedule/history",
				icon: <Clock className="h-3.5 w-3.5" />,
			},
		],
		// permissions: ['schedule:view'],
	},
	{
		id: "workplace",
		label: "Workplace",
		href: "/workplace",
		icon: <WorkplaceIcon />,
		// permissions: ['workplace:view'],
		// roles: ['admin', 'manager'],
	},
	{
		id: "clients",
		label: "Clients",
		href: "/clients",
		icon: <ClientsIcon size={20} />,
		// permissions: ['clients:view'],
		// groups: ['client_managers'],
	},
	{
		id: "forms",
		label: "Forms",
		href: "/forms",
		icon: <FormsIcon />,
		// permissions: ['forms:view'],
		// features: ['forms_module'],
	},
];

export const Sidebar = ({
	className,
	isOpen = false,
	onClose,
	...props
}: SidebarProps) => {
	const filteredNavigationItems = useFilteredNavigationItems(navigationItems);
	const {
		sidebarCollapsed,
		mobileMenuOpen,
		setMobileMenuOpen,
		expandedNavItem,
		toggleNavItem,
	} = useUIStore();
	const isMobile = typeof window !== "undefined" && window.innerWidth < 768;

	const handleItemClick = () => {
		// Close mobile menu when navigation item is clicked on mobile
		if (isMobile && mobileMenuOpen) {
			setMobileMenuOpen(false);
		}
		onClose?.();
	};

	return (
		<>
			{/* Mobile overlay - only show on mobile when drawer is open */}
			{isMobile && mobileMenuOpen && (
				<div
					className="fixed inset-0 z-40 bg-gray-600 opacity-75 transition-opacity md:hidden"
					onClick={onClose}
				/>
			)}

			{/* Sidebar */}
			<div
				className={cn(
					"bg-primary fixed inset-y-0 left-0 z-50 transform overflow-hidden transition-all duration-300 ease-in-out",
					// Mobile behavior: slide in/out based on mobileMenuOpen
					"md:relative md:translate-x-0",
					isMobile
						? mobileMenuOpen
							? "translate-x-0"
							: "-translate-x-full"
						: "translate-x-0",
					// Desktop width: collapsed (64px) or expanded (256px)
					sidebarCollapsed ? "md:w-16" : "md:w-64",
					// Mobile width: always full drawer width when open
					"w-64",
					className
				)}
				{...props}
			>
				<div className="flex h-full flex-col">
					{/* Header */}
					<div
						className={cn(
							"flex h-16 items-center",
							sidebarCollapsed
								? "justify-center px-2"
								: "justify-between px-4"
						)}
					>
						<div
							className={cn(
								"flex items-center",
								sidebarCollapsed
									? "justify-center"
									: "space-x-3"
							)}
						>
							<div className="flex h-8 w-8 items-center justify-center rounded-full bg-orange-500">
								<span className="text-sm font-bold text-white">
									UW
								</span>
							</div>
							{!sidebarCollapsed && (
								<div className="flex items-center space-x-1">
									<span className="max-w-1/2 text-sm font-medium text-white">
										<p className="truncate">
											University of Waterloo Camakkala sss
											sss
										</p>
									</span>
									<svg
										className="h-4 w-4 text-gray-400"
										fill="none"
										viewBox="0 0 24 24"
										stroke="currentColor"
									>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth={2}
											d="M19 9l-7 7-7-7"
										/>
									</svg>
								</div>
							)}
						</div>
					</div>

					{/* Navigation */}
					<nav
						className={cn(
							"flex-1 space-y-0.5 py-4",
							sidebarCollapsed ? "px-0" : "px-0"
						)}
					>
						{filteredNavigationItems.map((item) => (
							<div
								key={item.id}
								className={cn(
									expandedNavItem === item.id &&
										!sidebarCollapsed
										? "bg-[#043B6D] pb-2"
										: "px-4",
									sidebarCollapsed ? "px-2" : "px-4"
								)}
							>
								{/* Parent Navigation Item */}
								{item.children && item.children.length > 0 ? (
									<div
										className={cn(
											expandedNavItem === item.id &&
												!sidebarCollapsed
												? "px-3"
												: "",
											sidebarCollapsed ? "px-0" : "px-0"
										)}
									>
										<button
											onClick={() => {
												if (!sidebarCollapsed) {
													toggleNavItem(item.id);
												}
											}}
											className={cn(
												"group flex w-full cursor-pointer items-center rounded-md text-sm font-medium transition-all duration-200",
												sidebarCollapsed
													? "justify-center rounded-md px-2 py-3"
													: "px-3 py-2",
												expandedNavItem === item.id &&
													!sidebarCollapsed
													? "bg-[#043B6D] text-white"
													: "text-white hover:bg-gray-100 hover:text-gray-900"
											)}
											title={
												sidebarCollapsed
													? item.label
													: undefined
											}
										>
											<span
												className={cn(
													"h-[18px] w-[18px] flex-shrink-0",
													sidebarCollapsed
														? ""
														: "mr-3"
												)}
											>
												{item.icon}
											</span>
											{!sidebarCollapsed && (
												<>
													<span className="flex-1 text-left text-base tracking-[-0.16px]">
														{item.label}
													</span>
													<ChevronRight
														className={cn(
															"ml-auto h-4 w-4 transition-transform duration-200 ease-in-out",
															expandedNavItem ===
																item.id
																? "rotate-90"
																: "rotate-0"
														)}
													/>
												</>
											)}
										</button>

										{/* Child Navigation Items with smooth animation */}
										{!sidebarCollapsed && item.children && (
											<div
												className={cn(
													"overflow-hidden transition-all duration-300 ease-in-out",
													expandedNavItem === item.id
														? "max-h-96 opacity-100"
														: "max-h-0 opacity-0"
												)}
											>
												<div className="space-y-0">
													{item.children.map(
														(child) => (
															<NavLink
																key={child.id}
																to={child.href}
																onClick={
																	handleItemClick
																}
																className={({
																	isActive,
																}) =>
																	cn(
																		"group flex h-10 items-center rounded-md px-10 py-0.5 text-sm font-medium transition-colors",
																		isActive
																			? "bg-sidebar-accent text-sidebar-accent-foreground"
																			: "text-white hover:bg-gray-100 hover:text-gray-900"
																	)
																}
															>
																<span className="mr-3 h-3.5 w-3.5 flex-shrink-0">
																	{child.icon}
																</span>
																<span className="text-sm tracking-[-0.14px]">
																	{
																		child.label
																	}
																</span>
															</NavLink>
														)
													)}
												</div>
											</div>
										)}
									</div>
								) : (
									<NavLink
										to={item.href}
										onClick={handleItemClick}
										className={({ isActive }) =>
											cn(
												"group flex items-center rounded-md text-sm font-medium transition-colors",
												sidebarCollapsed
													? "justify-center px-2 py-3"
													: "px-3 py-2",
												isActive
													? "bg-sidebar-accent text-sidebar-accent-foreground"
													: "text-white hover:bg-gray-100 hover:text-gray-900"
											)
										}
										title={
											sidebarCollapsed
												? item.label
												: undefined
										}
									>
										<span
											className={cn(
												"h-[18px] w-[18px] flex-shrink-0",
												sidebarCollapsed ? "" : "mr-3"
											)}
										>
											{item.icon}
										</span>
										{!sidebarCollapsed && (
											<span className="flex-1 text-base tracking-[-0.16px]">
												{item.label}
											</span>
										)}
									</NavLink>
								)}
							</div>
						))}
					</nav>

					{/* Bottom section */}
					<div
						className={cn(
							"space-y-1",
							sidebarCollapsed ? "p-2 pb-4" : "p-4"
						)}
					>
						{/* Help */}
						<NavLink
							to="/help"
							className={({ isActive }) =>
								cn(
									"group flex items-center rounded-md text-sm font-medium transition-colors",
									sidebarCollapsed
										? "justify-center px-2 py-3"
										: "px-3 py-2",
									isActive
										? "bg-sidebar-accent text-white"
										: "text-white hover:bg-gray-100 hover:text-gray-900"
								)
							}
							title={sidebarCollapsed ? "Help" : undefined}
						>
							<div
								className={cn(
									"flex-shrink-0",
									sidebarCollapsed ? "" : "mr-3"
								)}
							>
								<HelpIcon />
							</div>
							{!sidebarCollapsed && <span>Help</span>}
						</NavLink>

						{/* Sign out */}
						<Button
							onClick={() => {
								console.log("Sign out clicked");
							}}
							className={cn(
								"group mb-4 flex h-auto w-full items-center justify-start rounded-md text-sm font-medium text-white hover:bg-gray-100 hover:text-gray-900",
								sidebarCollapsed
									? "justify-center px-2 py-3"
									: "gap-0 px-3 py-2"
							)}
							title={sidebarCollapsed ? "Sign out" : undefined}
						>
							<div
								className={cn(
									"flex-shrink-0",
									sidebarCollapsed ? "" : "mr-3"
								)}
							>
								<LogoutIcon />
							</div>
							{!sidebarCollapsed && <span>Sign out</span>}
						</Button>

						{/* Upgrade button */}
						<Button
							onClick={() => {
								console.log("Sign out clicked");
							}}
							className={cn(
								"focus:ring-primary hover:bg-primary/90 h-auto w-full cursor-pointer rounded-md border border-white bg-[#043B6D] px-3 py-2 text-sm font-medium text-white focus:ring-2 focus:ring-offset-2 focus:outline-none",
								sidebarCollapsed
									? "justify-center px-2 py-3"
									: "gap-0 px-3 py-2"
							)}
							title={sidebarCollapsed ? "Upgrade" : undefined}
						>
							<div
								className={cn(
									"h-5 w-5 flex-shrink-0",
									sidebarCollapsed ? "" : "mr-3"
								)}
							>
								<Zap className="h-4 w-4" />
							</div>
							{!sidebarCollapsed && <span>Upgrade</span>}
						</Button>
					</div>
				</div>
			</div>
		</>
	);
};
