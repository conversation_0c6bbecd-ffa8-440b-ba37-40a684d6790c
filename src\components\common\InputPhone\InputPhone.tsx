import React, { useState, useEffect, forwardRef } from 'react';
import { Controller } from 'react-hook-form';
import { CircleFlag } from 'react-circle-flags';
import { GlobeIcon, ChevronDownIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
// import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  FormControl,
  FormDescription,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import type {
  InputPhoneProps,
  DefaultPhoneProps,
  WithCountryDropdownProps,
  FormPhoneProps,
  CountryData,
} from './types';
import {
  parseAndValidatePhone,
  formatPhoneNumber,
  getCountryData,
  getAllCountries,
  getCountryCallingCode,
} from './utils';

// Default Phone Input Implementation
const DefaultPhoneInput = forwardRef<HTMLInputElement, DefaultPhoneProps>(
  ({ 
    value = '', 
    onChange, 
    onCountryChange,
    defaultCountry = 'US',
    showFlag = true,
    format = 'international',
    className,
    placeholder = 'Enter phone number',
    ...props 
  }, ref) => {
    const [countryData, setCountryData] = useState<CountryData | undefined>();
    const [displayFlag, setDisplayFlag] = useState<string>('');
    const [hasInitialized, setHasInitialized] = useState(false);

    // Initialize with default country
    useEffect(() => {
      if (defaultCountry && !hasInitialized) {
        const newCountryData = getCountryData(defaultCountry);
        if (newCountryData) {
          setCountryData(newCountryData);
          setDisplayFlag(defaultCountry.toLowerCase());
          
          // Set initial country calling code if no value
          if (!value && newCountryData.countryCallingCodes?.[0]) {
            onChange?.(newCountryData.countryCallingCodes[0]);
          }
        }
        setHasInitialized(true);
      }
    }, [defaultCountry, onChange, value, hasInitialized]);

    const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const inputValue = e.target.value;
      const phoneData = parseAndValidatePhone(inputValue);

      if (phoneData.country) {
        // Update country data and flag
        const newCountryData = getCountryData(phoneData.country);
        setCountryData(newCountryData);
        setDisplayFlag(phoneData.country.toLowerCase());
        onCountryChange?.(newCountryData);
      } else if (!inputValue.trim()) {
        // Clear country data if input is empty
        setCountryData(undefined);
        setDisplayFlag('');
        onCountryChange?.(undefined);
      }

      // Format the phone number based on the format prop
      const formattedValue = phoneData.formattedNumber || inputValue;
      onChange?.(format === 'international' ? formattedValue : inputValue);
    };

    return (
      <div className={cn(
        "flex items-center gap-2 relative bg-transparent transition-colors text-base rounded-md border border-input px-3 h-9 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed md:text-sm has-[input:focus]:outline-none has-[input:focus]:ring-1 has-[input:focus]:ring-ring",
        className
      )}>
        {showFlag && (
          <div className="w-4 h-4 rounded-full shrink-0 flex items-center justify-center">
            {displayFlag ? (
              <CircleFlag countryCode={displayFlag} height={16} width={16} />
            ) : (
              <GlobeIcon size={16} className="text-muted-foreground" />
            )}
          </div>
        )}
        <input
          ref={ref}
          value={value}
          onChange={handlePhoneChange}
          placeholder={placeholder}
          type="tel"
          autoComplete="tel"
          className="flex w-full border-none bg-transparent text-base transition-colors placeholder:text-muted-foreground outline-none h-9 py-1 p-0 leading-none md:text-sm"
          {...props}
        />
      </div>
    );
  }
);

// With Country Dropdown Implementation
const WithCountryDropdownPhoneInput = forwardRef<HTMLInputElement, WithCountryDropdownProps>(
  ({ 
    value = '', 
    onChange, 
    onCountryChange,
    onCountrySelect,
    defaultCountry = 'US',
    showFlag = true,
    format = 'international',
    className,
    dropdownClassName,
    searchable = true,
    placeholder = 'Enter phone number',
    ...props 
  }, ref) => {
    const [selectedCountry, setSelectedCountry] = useState<CountryData | undefined>();
    const [countries] = useState<CountryData[]>(getAllCountries());

    // Initialize with default country
    useEffect(() => {
      if (defaultCountry) {
        const countryData = getCountryData(defaultCountry);
        setSelectedCountry(countryData);
        onCountryChange?.(countryData);
      }
    }, [defaultCountry, onCountryChange]);

    const handleCountrySelect = (country: CountryData) => {
      setSelectedCountry(country);
      onCountryChange?.(country);
      onCountrySelect?.(country.alpha2);
      
      // Update phone number with new country code
      const callingCode = country.countryCallingCodes?.[0];
      if (callingCode) {
        onChange?.(callingCode);
      }
    };

    const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const inputValue = e.target.value;
      const phoneData = parseAndValidatePhone(inputValue);

      // Update selected country if detected from phone number
      if (phoneData.country) {
        const newCountryData = getCountryData(phoneData.country);
        if (newCountryData && newCountryData.alpha2 !== selectedCountry?.alpha2) {
          setSelectedCountry(newCountryData);
          onCountryChange?.(newCountryData);
        }
      }

      const formattedValue = phoneData.formattedNumber || inputValue;
      onChange?.(format === 'international' ? formattedValue : inputValue);
    };

    return (
      <div className="flex gap-0">
        {/* Country Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                "h-9 px-3 rounded-r-none border-r-0 bg-transparent hover:bg-muted",
                dropdownClassName
              )}
            >
              <div className="flex items-center gap-2">
                {showFlag && selectedCountry ? (
                  <CircleFlag countryCode={selectedCountry.alpha2.toLowerCase()} height={16} width={16} />
                ) : (
                  <GlobeIcon size={16} className="text-muted-foreground" />
                )}
                <span className="text-sm text-muted-foreground">
                  {selectedCountry?.countryCallingCodes?.[0] || '+1'}
                </span>
                <ChevronDownIcon size={12} className="text-muted-foreground" />
              </div>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-64 max-h-64 overflow-y-auto">
            {countries.map((country) => (
              <DropdownMenuItem
                key={country.alpha2}
                onClick={() => handleCountrySelect(country)}
                className="flex items-center gap-2 cursor-pointer"
              >
                <div className="w-4 h-4 shrink-0">
                  <CircleFlag countryCode={country.alpha2.toLowerCase()} height={16} width={16} />
                </div>
                <span className="flex-1 truncate">{country.name}</span>
                <span className="text-sm text-muted-foreground">
                  {country.countryCallingCodes?.[0]}
                </span>
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Phone Input */}
        <div className={cn(
          "flex items-center flex-1 relative bg-transparent transition-colors text-base rounded-md rounded-l-none border border-input border-l-0 px-3 h-9 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed md:text-sm has-[input:focus]:outline-none has-[input:focus]:ring-1 has-[input:focus]:ring-ring",
          className
        )}>
          <input
            ref={ref}
            value={value}
            onChange={handlePhoneChange}
            placeholder={placeholder}
            type="tel"
            autoComplete="tel"
            className="flex w-full border-none bg-transparent text-base transition-colors placeholder:text-muted-foreground outline-none h-9 py-1 p-0 leading-none md:text-sm"
            {...props}
          />
        </div>
      </div>
    );
  }
);

// Form Phone Input Implementation
function FormPhoneInput({
  name,
  control,
  label,
  description,
  rules,
  defaultCountry = 'US',
  showFlag = true,
  format = 'international',
  className,
  placeholder = 'Enter phone number',
  onCountryChange,
  ...props
}: FormPhoneProps) {
  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({ field, fieldState }) => (
        <FormItem>
          {label && <FormLabel>{label}</FormLabel>}
          <FormControl>
            <DefaultPhoneInput
              value={field.value}
              onChange={field.onChange}
              onBlur={field.onBlur}
              defaultCountry={defaultCountry}
              showFlag={showFlag}
              format={format}
              placeholder={placeholder}
              onCountryChange={onCountryChange}
              className={cn(
                fieldState.error && 'border-destructive focus-visible:ring-destructive',
                className
              )}
            />
          </FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

// Main InputPhone Component
export const InputPhone = forwardRef<HTMLInputElement, InputPhoneProps>(
  (props, ref) => {
    const { variant = 'default', ...otherProps } = props;

    switch (variant) {
      case 'with-country-dropdown':
        return <WithCountryDropdownPhoneInput ref={ref} {...otherProps as WithCountryDropdownProps} />;
      case 'form':
        return <FormPhoneInput {...otherProps as FormPhoneProps} />;
      default:
        return <DefaultPhoneInput ref={ref} {...otherProps as DefaultPhoneProps} />;
    }
  }
);

InputPhone.displayName = 'InputPhone';

DefaultPhoneInput.displayName = 'DefaultPhoneInput';
WithCountryDropdownPhoneInput.displayName = 'WithCountryDropdownPhoneInput';