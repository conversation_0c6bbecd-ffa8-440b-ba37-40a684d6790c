import type { <PERSON>a, StoryObj } from '@storybook/react-vite';
import { useForm } from 'react-hook-form';
import { Search, Mail, User, Lock } from 'lucide-react';
import { Form } from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { InputText } from './InputText';
import React from 'react';

const meta = {
  title: 'Components/InputText',
  component: InputText,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A unified input component that consolidates all shadcn/ui input patterns into a single component with variants.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['default', 'with-label', 'with-button', 'with-text', 'with-icon', 'file', 'disabled', 'form'],
    },
    size: {
      control: 'select',
      options: ['sm', 'md', 'lg'],
    },
  },
} satisfies Meta<typeof InputText>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    placeholder: 'Enter text...',
  },
};

export const AllVariants: Story = {
  render: () => (
    <div className="grid gap-8 p-6 max-w-2xl">
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Default</h3>
        <InputText placeholder="Default input" />
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">With Label</h3>
        <InputText 
          variant="with-label" 
          label="Email" 
          placeholder="Enter your email"
          type="email"
        />
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">With Button</h3>
        <InputText 
          variant="with-button" 
          placeholder="Enter your email"
          buttonText="Subscribe"
          onButtonClick={() => alert('Subscribed!')}
        />
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">With Text</h3>
        <div className="space-y-2">
          <InputText 
            variant="with-text" 
            leadingText="https://"
            placeholder="www.example.com"
          />
          <InputText 
            variant="with-text" 
            trailingText="@example.com"
            placeholder="username"
          />
          <InputText 
            variant="with-text" 
            leadingText="$"
            trailingText="USD"
            placeholder="0.00"
            type="number"
          />
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">With Icon</h3>
        <div className="space-y-2">
          <InputText 
            variant="with-icon" 
            icon={<Search className="h-4 w-4" />}
            placeholder="Search..."
          />
          <InputText 
            variant="with-icon" 
            icon={<Mail className="h-4 w-4" />}
            iconPosition="right"
            placeholder="Enter email"
            type="email"
          />
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">File Input</h3>
        <InputText 
          variant="file" 
          accept=".jpg,.png,.pdf"
        />
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Disabled</h3>
        <InputText 
          variant="disabled" 
          placeholder="This input is disabled"
          value="Disabled value"
        />
      </div>
    </div>
  ),
};

export const Sizes: Story = {
  render: () => (
    <div className="grid gap-6 p-6">
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Size Variations</h3>
        <div className="space-y-3">
          <InputText size="sm" placeholder="Small input" />
          <InputText size="md" placeholder="Medium input (default)" />
          <InputText size="lg" placeholder="Large input" />
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">With Icons - Different Sizes</h3>
        <div className="space-y-3">
          <InputText 
            variant="with-icon" 
            icon={<User className="h-3 w-3" />}
            size="sm" 
            placeholder="Small with icon" 
          />
          <InputText 
            variant="with-icon" 
            icon={<User className="h-4 w-4" />}
            size="md" 
            placeholder="Medium with icon" 
          />
          <InputText 
            variant="with-icon" 
            icon={<User className="h-5 w-5" />}
            size="lg" 
            placeholder="Large with icon" 
          />
        </div>
      </div>
    </div>
  ),
};

export const ErrorStates: Story = {
  render: () => (
    <div className="grid gap-6 p-6 max-w-lg">
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Error States</h3>
        <div className="space-y-3">
          <InputText 
            placeholder="Default with error" 
            error
            value="invalid-email"
          />
          <InputText 
            variant="with-label"
            label="Email"
            placeholder="Enter valid email" 
            error
            value="invalid@"
          />
          <InputText 
            variant="with-icon"
            icon={<Lock className="h-4 w-4" />}
            placeholder="Password with error" 
            type="password"
            error
          />
        </div>
      </div>
    </div>
  ),
};

// Form Integration Example
const FormExample = () => {
  const form = useForm({
    defaultValues: {
      username: '',
      email: '',
      website: '',
      bio: '',
    },
  });

  const onSubmit = (data: any) => {
    console.log('Form submitted:', data);
    alert('Form submitted! Check console for data.');
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 max-w-md">
        <InputText
          variant="form"
          name="username"
          control={form.control}
          label="Username"
          description="This is your public display name."
          rules={{ required: 'Username is required' }}
          placeholder="johndoe"
        />

        <InputText
          variant="form"
          name="email"
          control={form.control}
          label="Email"
          description="We'll never share your email with anyone else."
          type="email"
          rules={{ 
            required: 'Email is required',
            pattern: {
              value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
              message: 'Invalid email address'
            }
          }}
          placeholder="<EMAIL>"
        />

        <InputText
          variant="form"
          name="website"
          control={form.control}
          label="Website"
          description="Your personal website or portfolio."
          placeholder="https://johndoe.com"
        />

        <InputText
          variant="form"
          name="bio"
          control={form.control}
          label="Bio"
          description="Tell us a little bit about yourself."
          placeholder="I'm a software developer..."
        />

        <Button type="submit">Submit</Button>
      </form>
    </Form>
  );
};

export const FormIntegration: Story = {
  render: () => <FormExample />,
  parameters: {
    docs: {
      description: {
        story: 'Example of using InputText with React Hook Form. The form variant automatically handles validation, error display, and form integration.',
      },
    },
  },
};

export const Interactive: Story = {
  render: () => {
    const [searchValue, setSearchValue] = React.useState('');
    const [email, setEmail] = React.useState('');
    
    return (
      <div className="grid gap-6 p-6 max-w-lg">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Interactive Examples</h3>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">Search (controlled)</label>
            <InputText
              variant="with-icon"
              icon={<Search className="h-4 w-4" />}
              placeholder="Type to search..."
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
            />
            {searchValue && (
              <p className="text-sm text-muted-foreground">
                Searching for: "{searchValue}"
              </p>
            )}
          </div>

          <div className="space-y-2">
            <InputText
              variant="with-button"
              placeholder="Enter email for newsletter"
              buttonText="Subscribe"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              onButtonClick={() => {
                if (email) {
                  alert(`Subscribed: ${email}`);
                  setEmail('');
                } else {
                  alert('Please enter an email');
                }
              }}
            />
          </div>
        </div>
      </div>
    );
  },
};

export const Accessibility: Story = {
  render: () => (
    <div className="grid gap-6 p-6 max-w-lg">
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Accessibility Features</h3>
        <div className="space-y-4">
          <InputText
            variant="with-label"
            label="Full Name"
            placeholder="Enter your full name"
            id="full-name"
            aria-describedby="full-name-desc"
          />
          <p id="full-name-desc" className="text-sm text-muted-foreground">
            This field is required for account creation
          </p>

          <InputText
            variant="with-icon"
            icon={<Lock className="h-4 w-4" />}
            placeholder="Password"
            type="password"
            aria-label="Password input with lock icon"
            aria-describedby="password-requirements"
          />
          <p id="password-requirements" className="text-sm text-muted-foreground">
            Password must be at least 8 characters long
          </p>

          <InputText
            placeholder="Focus me with Tab key"
            onFocus={() => console.log('Input focused')}
            onBlur={() => console.log('Input blurred')}
          />
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Demonstrates proper accessibility features including ARIA labels, descriptions, and keyboard navigation support.',
      },
    },
  },
};