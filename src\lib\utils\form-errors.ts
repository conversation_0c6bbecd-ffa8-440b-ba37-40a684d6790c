import type { FieldValues, UseFormSetError, Path } from 'react-hook-form'

interface ApiError {
  success: false
  message: string
  error: string
  errors?: Record<string, string[]>
}

interface ErrorResponse {
  response?: {
    status: number
    data: ApiError
  }
}

export const mapServerErrorsToForm = <T extends FieldValues>(
  error: ErrorResponse,
  setError: UseFormSetError<T>
): boolean => {
  if (error.response?.status === 422) {
    const errorData = error.response.data as ApiError
    
    if (errorData.errors) {
      Object.entries(errorData.errors).forEach(([field, messages]) => {
        if (messages && messages.length > 0) {
          setError(field as Path<T>, {
            type: 'server',
            message: messages[0]
          })
        }
      })
      return true
    }
  }
  return false
}

export const clearServerErrors = <T extends FieldValues>(
  form: { clearErrors: (name?: keyof T | (keyof T)[]) => void }
) => {
  // Clear all errors when user starts typing again
  form.clearErrors()
} 