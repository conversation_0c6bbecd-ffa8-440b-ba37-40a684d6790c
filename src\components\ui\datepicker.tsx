import * as React from "react";
import { CalendarIcon } from "@radix-ui/react-icons";
import { format, isValid, getMonth, getYear } from "date-fns";
import { Button } from "./button";
import { Calendar } from "./calendar";
import { Popover, PopoverContent, PopoverTrigger } from "./popover";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "./select";
import { DayPicker, Matcher } from "react-day-picker";
import { cn } from "@/lib/utils";

export type DatePickerExistingProps = React.ComponentProps<typeof DayPicker>;

interface DatePickerProps {
	title?: string;
	date?: Date;
	setDate?: (date: Date | undefined) => void;
	className?: string;
	toDate?: Date;
	fromDate?: Date;
	mode?: "single" | "range";
	disabled?: Matcher | Matcher[];
	startYear?: number;
	endYear?: number;
}

type CombinedDatePickerProps = DatePickerProps & DatePickerExistingProps;

const months = [
	"January",
	"February",
	"March",
	"April",
	"May",
	"June",
	"July",
	"August",
	"September",
	"October",
	"November",
	"December",
];

function preserveDateOnly(date: Date): Date {
	const year = date.getFullYear();
	const month = date.getMonth();
	const day = date.getDate();

	return new Date(year, month, day, 12, 0, 0);
}

export function DatePicker({
	className,
	title,
	date: externalDate,
	setDate: externalSetDate,
	toDate,
	fromDate,
	disabled,
	startYear = getYear(new Date()) - 100,
	endYear = getYear(new Date()) + 100,
	...dayPickerProps
}: CombinedDatePickerProps) {
	const [internalDate, setInternalDate] = React.useState<Date>();
	const [popoverOpen, setPopoverOpen] = React.useState(false);
	const [viewMonth, setViewMonth] = React.useState<Date | undefined>(
		undefined
	);

	const date = externalDate ?? internalDate;
	const setDate = externalSetDate ?? setInternalDate;

	React.useEffect(() => {
		const initialDate =
			date && isValid(date)
				? new Date(date)
				: fromDate && isValid(fromDate)
					? new Date(fromDate)
					: new Date();
		setViewMonth(initialDate);
	}, []);

	React.useEffect(() => {
		if (externalDate && isValid(externalDate)) {
			setViewMonth(new Date(externalDate));
		}
	}, [externalDate]);

	React.useEffect(() => {
		if (
			fromDate &&
			viewMonth &&
			isValid(fromDate) &&
			isValid(viewMonth) &&
			viewMonth < fromDate
		) {
			setViewMonth(new Date(fromDate));
		}
	}, [fromDate, viewMonth]);

	const years = React.useMemo(() => {
		return Array.from({ length: endYear - startYear + 1 }, (_, i) =>
			(startYear + i).toString()
		);
	}, [startYear, endYear]);

	const handleMonthChange = React.useCallback(
		(month: string) => {
			if (!viewMonth) return;

			const monthIndex = months.indexOf(month);
			if (monthIndex === -1) return;

			const newViewMonth = new Date(viewMonth);
			newViewMonth.setDate(1);
			newViewMonth.setMonth(monthIndex);

			if (fromDate && isValid(fromDate) && newViewMonth < fromDate) {
				const constrainedDate = new Date(fromDate);
				constrainedDate.setDate(1);
				setViewMonth(constrainedDate);
			} else {
				setViewMonth(newViewMonth);
			}
		},
		[viewMonth, fromDate]
	);

	const handleYearChange = React.useCallback(
		(yearStr: string) => {
			if (!viewMonth) return;

			const yearNum = parseInt(yearStr);
			if (isNaN(yearNum)) return;

			const newViewMonth = new Date(viewMonth);
			newViewMonth.setDate(1);
			newViewMonth.setFullYear(yearNum);

			if (fromDate && isValid(fromDate) && newViewMonth < fromDate) {
				const constrainedDate = new Date(fromDate);
				constrainedDate.setDate(1);
				setViewMonth(constrainedDate);
			} else {
				setViewMonth(newViewMonth);
			}
		},
		[viewMonth, fromDate]
	);

	const handleSelect = React.useCallback(
		(selectedDate: Date | undefined) => {
			if (selectedDate && isValid(selectedDate)) {
				const normalizedDate = preserveDateOnly(selectedDate);

				setDate(normalizedDate);
				setPopoverOpen(false);
				setViewMonth(selectedDate);
			}
		},
		[setDate]
	);

	const displayMonth = viewMonth || new Date();
	const currentMonth = months[getMonth(displayMonth)];
	const currentYear = getYear(displayMonth).toString();

	return (
		<Popover onOpenChange={setPopoverOpen} open={popoverOpen}>
			<PopoverTrigger asChild>
				<Button
					variant="outline"
					className={cn(
						"w-fit justify-start gap-1 p-2 text-left font-normal",
						!date && "text-muted-foreground",
						className
					)}
				>
					<CalendarIcon className="h-4 w-4" />
					{date && isValid(date) ? (
						format(date, "PPP")
					) : (
						<span>{title || "Pick a date"}</span>
					)}
				</Button>
			</PopoverTrigger>
			<PopoverContent className="w-auto p-0" side="bottom">
				<div className="flex justify-between p-2">
					<Select
						onValueChange={handleMonthChange}
						value={currentMonth}
						defaultValue={currentMonth}
					>
						<SelectTrigger className="w-[110px]">
							<SelectValue placeholder="Month" />
						</SelectTrigger>
						<SelectContent>
							{months.map((month) => (
								<SelectItem key={month} value={month}>
									{month}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
					<Select
						onValueChange={handleYearChange}
						value={currentYear}
						defaultValue={currentYear}
					>
						<SelectTrigger className="w-[110px]">
							<SelectValue placeholder="Year" />
						</SelectTrigger>
						<SelectContent>
							{years.map((year) => (
								<SelectItem key={year} value={year}>
									{year}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				</div>
				<Calendar
					{...dayPickerProps}
					fixedWeeks
					mode="single"
					selected={date}
					onSelect={handleSelect}
					initialFocus
					month={displayMonth}
					fromDate={fromDate}
					toDate={toDate}
					onMonthChange={setViewMonth}
					disabled={disabled}
				/>
			</PopoverContent>
		</Popover>
	);
}

export default DatePicker;
