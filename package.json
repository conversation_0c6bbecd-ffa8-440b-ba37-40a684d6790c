{"name": "migranium-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/vite": "^4.1.10", "@tanstack/react-query": "^4.39.2", "@tanstack/react-query-devtools": "^4.39.2", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "country-data-list": "^1.4.1", "date-fns": "^4.1.0", "init": "^0.1.2", "input-otp": "^1.4.2", "libphonenumber-js": "^1.12.9", "lucide-react": "^0.516.0", "next-themes": "^0.4.6", "react": "^19.1.0", "react-circle-flags": "^0.0.23", "react-day-picker": "^9.7.0", "react-dom": "^19.1.0", "react-hook-form": "^7.58.1", "react-router": "^7.6.2", "shadcn": "^2.6.4", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^3.25.67", "zustand": "^5.0.5"}, "devDependencies": {"@chromatic-com/storybook": "4.0.0", "@eslint/js": "^9.25.0", "@storybook/addon-a11y": "9.0.11", "@storybook/addon-docs": "9.0.11", "@storybook/addon-onboarding": "9.0.11", "@storybook/addon-vitest": "9.0.11", "@storybook/react-vite": "9.0.11", "@testing-library/react": "^16.3.0", "@types/node": "^24.0.3", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "@vitest/browser": "^3.2.3", "@vitest/coverage-v8": "^3.2.3", "eslint": "^9.25.0", "eslint-plugin-prettier": "^5.5.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "eslint-plugin-storybook": "9.0.11", "globals": "^16.0.0", "playwright": "^1.53.0", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.12", "storybook": "9.0.11", "tailwindcss": "^4.1.10", "tw-animate-css": "^1.3.4", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vitest": "^3.2.3"}, "packageManager": "pnpm@9.15.3+sha512.1f79bc245a66eb0b07c5d4d83131240774642caaa86ef7d0434ab47c0d16f66b04e21e0c086eb61e62c77efc4d7f7ec071afad3796af64892fae66509173893a"}