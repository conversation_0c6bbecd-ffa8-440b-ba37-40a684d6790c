import type { ReactNode } from "react";

export interface SidebarProps {
	className?: string;
	isOpen?: boolean;
	onClose?: () => void;
}

export interface NavigationItem {
	id: string;
	label: string;
	href: string;
	icon?: ReactNode;
	badge?: string | number;
	children?: NavigationItem[];
	// Permission-based access control
	permissions?: string[];
	roles?: string[];
	groups?: string[];
	features?: string[];
	requireAll?: boolean; // If true, user must have ALL specified permissions/roles/groups
}

export interface SidebarNavigationProps {
	items: NavigationItem[];
	onItemClick?: (item: NavigationItem) => void;
}
