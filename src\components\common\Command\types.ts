import type { ReactNode } from 'react';

export interface CommandItem {
  id: string;
  label: string;
  value?: string;
  icon?: ReactNode;
  shortcut?: string;
  disabled?: boolean;
  onSelect?: () => void;
}

export interface CommandGroup {
  heading?: string;
  items: CommandItem[];
}

export interface CommandPreset {
  label: string;
  value: string;
  shortcut?: string;
}

export type CommandVariant = 
  | 'default'           // Basic command menu
  | 'dialog'            // Command in dialog (⌘K style)
  | 'combobox'          // Command in popover for selection
  | 'dropdown'          // Command in dropdown menu
  | 'inline'            // Always visible command
  | 'search-only';      // Just search input

export type CommandSize = 'sm' | 'md' | 'lg';

export interface BaseCommandProps {
  variant?: CommandVariant;
  size?: CommandSize;
  placeholder?: string;
  emptyMessage?: string;
  className?: string;
  disabled?: boolean;
}

export interface DefaultCommandProps extends BaseCommandProps {
  variant?: 'default';
  groups: CommandGroup[];
  showSearch?: boolean;
  onItemSelect?: (item: CommandItem) => void;
}

export interface DialogCommandProps extends BaseCommandProps {
  variant: 'dialog';
  groups: CommandGroup[];
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  trigger?: ReactNode;
  shortcut?: string; // e.g., "⌘K"
  onItemSelect?: (item: CommandItem) => void;
}


export interface ComboboxCommandProps extends BaseCommandProps {
  variant: 'combobox';
  options: CommandItem[];
  value?: string | string[];
  onChange?: (value: string | string[]) => void;
  trigger?: ReactNode;
  searchable?: boolean;
  allowClear?: boolean;
  multiple?: boolean;
  maxSelectedItems?: number;
}


export interface DropdownCommandProps extends BaseCommandProps {
  variant: 'dropdown';
  groups: CommandGroup[];
  trigger: ReactNode;
  onItemSelect?: (item: CommandItem) => void;
  align?: 'start' | 'end' | 'center';
  side?: 'top' | 'bottom' | 'left' | 'right';
}

// Inline Command Props
export interface InlineCommandProps extends BaseCommandProps {
  variant: 'inline';
  groups: CommandGroup[];
  showSearch?: boolean;
  onItemSelect?: (item: CommandItem) => void;
}

// Search-only Command Props
export interface SearchOnlyCommandProps extends BaseCommandProps {
  variant: 'search-only';
  onSearch?: (query: string) => void;
  onSubmit?: (query: string) => void;
  loading?: boolean;
}

export type CommandProps = 
  | DefaultCommandProps
  | DialogCommandProps
  | ComboboxCommandProps
  | DropdownCommandProps
  | InlineCommandProps
  | SearchOnlyCommandProps;