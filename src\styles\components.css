/* Component-specific styles */
@layer components {
  /* Custom component styles will go here */
  .migranium-button {
    @apply transition-all duration-200 ease-in-out;
  }
  
  .migranium-card {
    @apply bg-card text-card-foreground border border-border rounded-lg shadow-sm;
  }
  
  .migranium-input {
    @apply bg-background border border-input rounded-md px-3 py-2 text-sm placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }
} 