// Command.stories.tsx
import type { Meta, StoryObj } from '@storybook/react-vite'
import * as React from 'react'
import { 
  Calendar, 
  Calculator, 
  CreditCard, 
  Settings, 
  Smile, 
  User,
  Mail,
  MessageSquare,
  PlusCircle,
  UserPlus,
  Github,
  MoreHorizontal
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Command } from './Command'
import type { CommandGroup, CommandItem } from './types'

// Mock action function for Storybook
const action = (name: string) => (...args: any[]) => {
  console.log(`Action: ${name}`, args)
}

const meta: Meta<typeof Command> = {
  title: 'Components/Command',
  component: Command,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A unified command component that consolidates all shadcn/ui command patterns into a single component with variants.',
      },
    },
  },
  argTypes: {
    variant: {
      control: 'select',
      options: ['default', 'dialog', 'combobox', 'dropdown', 'inline', 'search-only'],
      description: 'The variant of the command component',
    },
    size: {
      control: 'select',
      options: ['sm', 'md', 'lg'],
      description: 'Size of the command component',
    },
    disabled: {
      control: 'boolean',
      description: 'Whether the command is disabled',
    },
  },
}

export default meta
type Story = StoryObj<typeof Command>

// Sample data
const sampleGroups: CommandGroup[] = [
  {
    heading: 'Suggestions',
    items: [
      {
        id: 'calendar',
        label: 'Calendar',
        icon: <Calendar />,
        onSelect: action('calendar-selected'),
      },
      {
        id: 'search-emoji',
        label: 'Search Emoji',
        icon: <Smile />,
        onSelect: action('emoji-selected'),
      },
      {
        id: 'calculator',
        label: 'Calculator',
        icon: <Calculator />,
        disabled: true,
        onSelect: action('calculator-selected'),
      },
    ],
  },
  {
    heading: 'Settings',
    items: [
      {
        id: 'profile',
        label: 'Profile',
        icon: <User />,
        shortcut: '⌘P',
        onSelect: action('profile-selected'),
      },
      {
        id: 'billing',
        label: 'Billing',
        icon: <CreditCard />,
        shortcut: '⌘B',
        onSelect: action('billing-selected'),
      },
      {
        id: 'settings',
        label: 'Settings',
        icon: <Settings />,
        shortcut: '⌘S',
        onSelect: action('settings-selected'),
      },
    ],
  },
]

const frameworkOptions: CommandItem[] = [
  { id: 'nextjs', label: 'Next.js', value: 'nextjs' },
  { id: 'sveltekit', label: 'SvelteKit', value: 'sveltekit' },
  { id: 'nuxtjs', label: 'Nuxt.js', value: 'nuxtjs' },
  { id: 'remix', label: 'Remix', value: 'remix' },
  { id: 'astro', label: 'Astro', value: 'astro' },
  { id: 'vite', label: 'Vite', value: 'vite' },
  { id: 'gatsby', label: 'Gatsby', value: 'gatsby' },
]

const userOptions: CommandItem[] = [
  { id: 'user1', label: 'John Doe', value: 'john', icon: <User /> },
  { id: 'user2', label: 'Jane Smith', value: 'jane', icon: <User /> },
  { id: 'user3', label: 'Bob Johnson', value: 'bob', icon: <User /> },
  { id: 'user4', label: 'Alice Brown', value: 'alice', icon: <User /> },
  { id: 'user5', label: 'Charlie Wilson', value: 'charlie', icon: <User /> },
]

// Stories
export const Default: Story = {
  args: {
    variant: 'default',
    groups: sampleGroups,
    size: 'md',
    onItemSelect: action('item-selected'),
  },
}

export const AllVariants: Story = {
  render: () => (
    <div className="grid gap-8 p-6 max-w-6xl">
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Default Command</h3>
        <Command
          variant="default"
          groups={sampleGroups}
          onItemSelect={action('default-item-selected')}
        />
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Dialog Command (Press ⌘K)</h3>
        <Command
          variant="dialog"
          groups={sampleGroups}
          onItemSelect={action('dialog-item-selected')}
        />
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Combobox - Single Select</h3>
        <Command
          variant="combobox"
          options={frameworkOptions}
          placeholder="Select framework..."
          onChange={action('combobox-changed')}
        />
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Combobox - Multi Select</h3>
        <Command
          variant="combobox"
          options={userOptions}
          placeholder="Select users..."
          multiple
          maxSelectedItems={3}
          allowClear
          onChange={action('multi-combobox-changed')}
        />
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Dropdown Command</h3>
        <Command
          variant="dropdown"
          groups={sampleGroups}
          trigger={
            <Button variant="outline">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          }
          onItemSelect={action('dropdown-item-selected')}
        />
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Inline Command</h3>
        <Command
          variant="inline"
          groups={sampleGroups}
          onItemSelect={action('inline-item-selected')}
        />
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Search Only</h3>
        <Command
          variant="search-only"
          placeholder="Search anything..."
          onSearch={action('search-query')}
          onSubmit={action('search-submit')}
        />
      </div>
    </div>
  ),
}

export const DialogCommand: Story = {
  args: {
    variant: 'dialog',
    groups: sampleGroups,
    shortcut: '⌘K',
    onItemSelect: action('dialog-item-selected'),
  },
}

export const ComboboxSingle: Story = {
  args: {
    variant: 'combobox',
    options: frameworkOptions,
    placeholder: 'Select framework...',
    onChange: action('combobox-changed'),
  },
}

export const ComboboxMultiple: Story = {
  args: {
    variant: 'combobox',
    options: userOptions,
    placeholder: 'Select users...',
    multiple: true,
    maxSelectedItems: 3,
    allowClear: true,
    onChange: action('multi-combobox-changed'),
  },
}

export const DropdownCommand: Story = {
  args: {
    variant: 'dropdown',
    groups: sampleGroups,
    trigger: (
      <Button variant="outline">
        Open Menu <MoreHorizontal className="ml-2 h-4 w-4" />
      </Button>
    ),
    onItemSelect: action('dropdown-item-selected'),
  },
}

export const InlineCommand: Story = {
  args: {
    variant: 'inline',
    groups: sampleGroups,
    showSearch: true,
    onItemSelect: action('inline-item-selected'),
  },
}

export const SearchOnly: Story = {
  args: {
    variant: 'search-only',
    placeholder: 'Search anything...',
    onSearch: action('search-query'),
    onSubmit: action('search-submit'),
  },
}

export const SearchOnlyWithLoading: Story = {
  args: {
    variant: 'search-only',
    placeholder: 'Search with loading...',
    loading: true,
    onSearch: action('search-query'),
    onSubmit: action('search-submit'),
  },
}

export const Sizes: Story = {
  render: () => (
    <div className="grid gap-6 p-6">
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Small Size</h3>
        <Command
          variant="default"
          groups={sampleGroups}
          size="sm"
          onItemSelect={action('small-item-selected')}
        />
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Medium Size (Default)</h3>
        <Command
          variant="default"
          groups={sampleGroups}
          size="md"
          onItemSelect={action('medium-item-selected')}
        />
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Large Size</h3>
        <Command
          variant="default"
          groups={sampleGroups}
          size="lg"
          onItemSelect={action('large-item-selected')}
        />
      </div>
    </div>
  ),
}

export const DisabledState: Story = {
  render: () => (
    <div className="grid gap-6 p-6">
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Disabled Default Command</h3>
        <Command
          variant="default"
          groups={sampleGroups}
          disabled
          onItemSelect={action('disabled-item-selected')}
        />
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Disabled Combobox</h3>
        <Command
          variant="combobox"
          options={frameworkOptions}
          placeholder="Select framework..."
          disabled
          onChange={action('disabled-combobox-changed')}
        />
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Disabled Search Only</h3>
        <Command
          variant="search-only"
          placeholder="Search anything..."
          disabled
          onSearch={action('disabled-search-query')}
        />
      </div>
    </div>
  ),
}

export const CustomTriggers: Story = {
  render: () => (
    <div className="grid gap-6 p-6">
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Custom Dialog Trigger</h3>
        <Command
          variant="dialog"
          groups={sampleGroups}
          trigger={
            <Button variant="secondary">
              <MessageSquare className="mr-2 h-4 w-4" />
              Open Command Palette
            </Button>
          }
          onItemSelect={action('custom-dialog-item-selected')}
        />
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Custom Combobox Trigger</h3>
        <Command
          variant="combobox"
          options={frameworkOptions}
          trigger={
            <Button variant="outline" className="w-[250px] justify-between">
              Choose your framework
              <PlusCircle className="ml-2 h-4 w-4" />
            </Button>
          }
          onChange={action('custom-combobox-changed')}
        />
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Icon-only Dropdown Trigger</h3>
        <Command
          variant="dropdown"
          groups={sampleGroups}
          trigger={
            <Button variant="ghost" size="icon">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          }
          onItemSelect={action('icon-dropdown-item-selected')}
        />
      </div>
    </div>
  ),
}

export const EmptyStates: Story = {
  render: () => (
    <div className="grid gap-6 p-6">
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Empty Command (No Groups)</h3>
        <Command
          variant="default"
          groups={[]}
          emptyMessage="No commands available"
          onItemSelect={action('empty-item-selected')}
        />
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Empty Combobox (No Options)</h3>
        <Command
          variant="combobox"
          options={[]}
          placeholder="No options available..."
          emptyMessage="No frameworks found"
          onChange={action('empty-combobox-changed')}
        />
      </div>
    </div>
  ),
}

export const Advanced: Story = {
  render: () => {
    const [selectedFramework, setSelectedFramework] = React.useState<string>('')
    const [selectedUsers, setSelectedUsers] = React.useState<string[]>([])
    const [searchQuery, setSearchQuery] = React.useState<string>('')
    const [isSearchLoading, setIsSearchLoading] = React.useState<boolean>(false)

    const handleSearch = (query: string) => {
      setSearchQuery(query)
      setIsSearchLoading(true)
      setTimeout(() => setIsSearchLoading(false), 1000)
    }

    const handleMultipleUsersChange = (value: string | string[]) => {
      if (Array.isArray(value)) {
        setSelectedUsers(value)
      } else {
        setSelectedUsers([value])
      }
    }

    return (
      <div className="grid gap-8 p-6 max-w-4xl">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Interactive Examples</h3>
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <label className="text-sm font-medium">Framework Selection</label>
              <Command
                variant="combobox"
                options={frameworkOptions}
                value={selectedFramework}
                placeholder="Select framework..."
                onChange={(value) => {
                  if (typeof value === 'string') {
                    setSelectedFramework(value)
                  }
                }}
                allowClear
              />
              {selectedFramework && (
                <p className="text-sm text-muted-foreground">
                  Selected: {frameworkOptions.find(f => f.value === selectedFramework)?.label}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">User Selection (Max 2)</label>
              <Command
                variant="combobox"
                options={userOptions}
                placeholder="Select users..."
                multiple
                maxSelectedItems={2}
                onChange={handleMultipleUsersChange}
                allowClear
              />
              {selectedUsers.length > 0 && (
                <p className="text-sm text-muted-foreground">
                  Selected: {selectedUsers.join(', ')}
                </p>
              )}
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Live Search</h3>
          <Command
            variant="search-only"
            placeholder="Search with live feedback..."
            loading={isSearchLoading}
            onSearch={handleSearch}
            onSubmit={(query) => alert(`Searching for: ${query}`)}
          />
          {searchQuery && (
            <p className="text-sm text-muted-foreground">
              Current query: "{searchQuery}" {isSearchLoading && '(searching...)'}
            </p>
          )}
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Complex Command Groups</h3>
          <Command
            variant="inline"
            groups={[
              {
                heading: 'Quick Actions',
                items: [
                  {
                    id: 'new-user',
                    label: 'Add New User',
                    icon: <UserPlus />,
                    shortcut: '⌘U',
                    onSelect: () => alert('Adding new user...'),
                  },
                  {
                    id: 'send-email',
                    label: 'Send Email',
                    icon: <Mail />,
                    shortcut: '⌘E',
                    onSelect: () => alert('Opening email composer...'),
                  },
                ],
              },
              {
                heading: 'External Links',
                items: [
                  {
                    id: 'github',
                    label: 'Open GitHub',
                    icon: <Github />,
                    onSelect: () => window.open('https://github.com', '_blank'),
                  },
                ],
              },
              {
                heading: 'System',
                items: [
                  {
                    id: 'system-settings',
                    label: 'System Settings',
                    icon: <Settings />,
                    shortcut: '⌘,',
                    disabled: true,
                  },
                ],
              },
            ]}
          />
        </div>
      </div>
    )
  },
}

export const Accessibility: Story = {
  render: () => (
    <div className="grid gap-6 p-6">
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Keyboard Navigation Test</h3>
        <p className="text-sm text-muted-foreground">
          Use Tab, Arrow keys, Enter, and Escape to navigate. Press ⌘K for dialog.
        </p>
        <div className="grid gap-4 md:grid-cols-2">
          <Command
            variant="combobox"
            options={frameworkOptions}
            placeholder="Navigate with keyboard..."
            onChange={action('keyboard-combobox-changed')}
          />
          <Command
            variant="dialog"
            groups={sampleGroups}
            onItemSelect={action('keyboard-dialog-item-selected')}
          />
        </div>
      </div>
    </div>
  ),
}