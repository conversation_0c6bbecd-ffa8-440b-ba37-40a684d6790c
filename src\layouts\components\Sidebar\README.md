# Sidebar with Role-Based Access Control

The Sidebar component now supports role-based access control (RBAC) that filters navigation items based on user permissions, roles, groups, and features.

## Permission Configuration

Each navigation item can be configured with the following access control properties:

```typescript
interface NavigationItem {
  id: string;
  label: string;
  href: string;
  icon?: ReactNode;
  isActive?: boolean;
  badge?: string | number;
  children?: NavigationItem[];
  
  // Permission-based access control
  permissions?: string[];  // Required permissions
  roles?: string[];       // Required roles
  groups?: string[];      // Required groups
  features?: string[];    // Required features
  requireAll?: boolean;   // If true, user must have ALL specified permissions/roles/groups
}
```

## Access Control Logic

- **No restrictions**: If no permissions, roles, groups, or features are specified, the item is accessible to all users
- **requireAll = false** (default): User needs ANY of the specified permissions/roles/groups/features
- **requireAll = true**: User needs ALL of the specified permissions/roles/groups/features

## Example Usage

```typescript
const navigationItems: NavigationItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    href: '/',
    // No restrictions - accessible to all users
  },
  {
    id: 'analytics',
    label: 'Analytics',
    href: '/analytics',
    permissions: ['analytics:view'],
    roles: ['admin', 'manager', 'analyst'],
    // User needs analytics:view permission OR admin/manager/analyst role
  },
  {
    id: 'admin-panel',
    label: 'Admin Panel',
    href: '/admin',
    permissions: ['admin:manage'],
    roles: ['admin'],
    requireAll: true,
    // User needs BOTH admin:manage permission AND admin role
  }
];
```

## Permission Types

The system supports four types of access control:

1. **Permissions**: Specific action-based permissions (e.g., `analytics:view`, `users:edit`)
2. **Roles**: User roles (e.g., `admin`, `manager`, `user`)
3. **Groups**: User groups (e.g., `hr_team`, `finance_team`)
4. **Features**: Feature flags (e.g., `advanced_analytics`, `beta_features`)

## Integration with Permission Store

The sidebar integrates with the existing permission system:
- Uses `usePermissions()` hook to access user permissions
- Leverages the `useAuthStore` for role and group checks
- Supports scoped permissions with location/station context

## Hierarchical Filtering

Child navigation items are also filtered based on permissions. If a parent item has no accessible children after filtering, the parent item is also removed from the navigation. 