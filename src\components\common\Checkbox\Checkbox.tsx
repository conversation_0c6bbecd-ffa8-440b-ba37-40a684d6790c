import * as React from "react";
import { Label } from "@/components/ui/label";
import type{ CheckboxProps } from "./types";
import { Checkbox as CheckboxBase } from "@/components/ui/checkbox";


const Checkbox = React.forwardRef<
  React.ElementRef<typeof CheckboxBase>,
  CheckboxProps
>(({
  variant = 'default',
  id,
  label,
  description,
  className,
  disabled,
  ...props
}, ref) => {
  const checkboxId = id || React.useId();
  
  const checkboxElement = (
    <CheckboxBase
      ref={ref}
      id={checkboxId}
      disabled={disabled}
      className={className}
      {...props}
    />
  );
  
  switch (variant) {
    case 'with-text':
      return (
        <div className="items-start flex space-x-2">
          {checkboxElement}
          <div className="grid gap-1.5 leading-none">
            {label && (
              <Label 
                htmlFor={checkboxId}
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                {label}
              </Label>
            )}
            {description && (
              <p className="text-sm text-muted-foreground">
                {description}
              </p>
            )}
          </div>
        </div>
      );
      
    default: 
      if (!label) {
        return checkboxElement;
      }
    
      return (
        <div className="flex items-center space-x-2">
          {checkboxElement}
          <Label 
            htmlFor={checkboxId}
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            {label}
          </Label>
        </div>
      );
  }
});

Checkbox.displayName = "Checkbox";

export { Checkbox };
