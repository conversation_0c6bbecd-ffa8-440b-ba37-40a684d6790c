// types.ts
export interface ComboboxOption {
    value: string;
    label: string;
    disabled?: boolean;
    icon?: React.ReactNode;
    description?: string;
  }
  
  export interface ComboboxGroup {
    label: string;
    options: ComboboxOption[];
  }
  
  export interface ComboboxProps {
    variant?: 
      | "default"
      | "responsive"
      | "form"
      | "dropdown-menu"
      | "searchable"
      | "multi-select"
      | "creatable"
      | "with-icons"
      | "grouped";
    
   
    value?: string | string[];
    onValueChange?: (value: string | string[]) => void;
    placeholder?: string;
    disabled?: boolean;
    
    className?: string;
    
    options?: ComboboxOption[];
    
    groups?: ComboboxGroup[];
    
    searchPlaceholder?: string;
    
    noResultsText?: string;
    
    searchable?: boolean;
    
    onCreateOption?: (value: string) => void;
    
    createOptionText?: string;
    
    maxSelected?: number;
    
    showSelectedCount?: boolean;
    
    triggerClassName?: string;
    
    contentClassName?: string;
    
    size?: "sm" | "md" | "lg";
    
    mobileBreakpoint?: string;
    
    triggerIcon?: React.ReactNode;
    
    menuLabel?: string;
  }
  
  /**
   * Utility type for form integration
   */
  export interface ComboboxFormProps extends Omit<ComboboxProps, 'value' | 'onValueChange'> {
    name: string;
    control?: any;
  }
  
  /**
   * Utility type for creating option objects
   */
  export type CreateComboboxOption = (
    value: string,
    label?: string,
    extra?: Partial<Omit<ComboboxOption, 'value' | 'label'>>
  ) => ComboboxOption;
  
 
  export const createOption: CreateComboboxOption = (value, label, extra = {}) => ({
    value,
    label: label || value,
    ...extra,
  });
  
  /**
   * Helper function to create grouped options
   */
  export const createGroup = (label: string, options: ComboboxOption[]): ComboboxGroup => ({
    label,
    options,
  });
  
  /**
   * Predefined size configurations
   */
  export const COMBOBOX_SIZES = {
    sm: { height: "h-8", text: "text-xs" },
    md: { height: "h-9", text: "text-sm" },
    lg: { height: "h-10", text: "text-base" }
  } as const;
  
  /**
   * Predefined variant configurations
   */
  export const COMBOBOX_VARIANTS = {
    default: "Basic combobox with popover",
    responsive: "Responsive combobox (drawer on mobile)",
    form: "Form-optimized combobox",
    "dropdown-menu": "Dropdown menu styled combobox",
    searchable: "Searchable combobox",
    "multi-select": "Multi-selection combobox",
    creatable: "Creatable combobox (add new options)",
    "with-icons": "Combobox with option icons",
    grouped: "Grouped options combobox"
  } as const;