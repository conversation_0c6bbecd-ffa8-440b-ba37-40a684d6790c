import type { Control, RegisterOptions } from 'react-hook-form';

export type InputOTPVariant = 'default' | 'with-separator' | 'pattern' | 'form' | 'disabled';

export interface BaseInputOTPProps {
  variant?: InputOTPVariant;
  maxLength?: number;
  value?: string;
  onChange?: (value: string) => void;
  className?: string;
  containerClassName?: string;
  disabled?: boolean;
  pattern?: RegExp;
}

export interface DefaultOTPProps extends BaseInputOTPProps {
  variant?: 'default';
}

export interface WithSeparatorOTPProps extends BaseInputOTPProps {
  variant: 'with-separator';
  groupSizes?: number[];
  separatorChar?: string;
}

export interface PatternOTPProps extends BaseInputOTPProps {
  variant: 'pattern';
  pattern: RegExp;
  patternDescription?: string;
}

export interface FormOTPProps extends BaseInputOTPProps {
  variant: 'form';
  name: string;
  control: Control<any>;
  label?: string;
  description?: string;
  rules?: RegisterOptions;
}

export interface DisabledOTPProps extends BaseInputOTPProps {
  variant: 'disabled';
  disabled: true;
  value: string;
}

export type InputOTPProps = 
  | DefaultOTPProps
  | WithSeparatorOTPProps
  | PatternOTPProps
  | FormOTPProps
  | DisabledOTPProps;